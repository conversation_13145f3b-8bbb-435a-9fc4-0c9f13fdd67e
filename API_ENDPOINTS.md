# Church Management API - Complete Endpoints Documentation

## 🔐 Authentication Endpoints

### POST /api/auth/admin/login
Admin login dengan Supabase Auth credentials
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### POST /api/auth/member/login
Member login dengan encrypted password
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### POST /api/auth/member/check
Check if member exists and has password set
```json
{
  "email": "<EMAIL>"
}
```

### POST /api/auth/member/reset-password
Reset member password (first login)
```json
{
  "memberId": "uuid",
  "currentPassword": "oldpass",
  "newPassword": "newpass123"
}
```

### POST /api/auth/refresh
Refresh access token
```json
{
  "refresh_token": "refresh_token_here"
}
```

### GET /api/auth/me
Get current user info (requires auth)

### POST /api/auth/logout
Logout user (requires auth)

---

## 👥 Member Endpoints

### GET /api/members
Get all members with pagination and search
- Query params: `page`, `limit`, `search`, `sortBy`, `sortOrder`
- Requires: Authentication

### GET /api/members/:id
Get member by ID
- Requires: Authentication

### POST /api/members
Create new member
- Requires: Admin authentication
```json
{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890",
  "address": "123 Main St",
  "date_of_birth": "1990-01-01",
  "gender": "male",
  "marital_status": "single",
  "emergency_contact_name": "Jane Doe",
  "emergency_contact_phone": "+1234567891",
  "notes": "Additional notes",
  "cell_group_id": "uuid",
  "district_id": "uuid"
}
```

### PUT /api/members/:id
Update member
- Requires: Admin authentication

### DELETE /api/members/:id
Soft delete member (set status to inactive)
- Requires: Admin authentication

### GET /api/members/:id/attendance
Get member attendance history
- Query params: `page`, `limit`
- Requires: Authentication

### POST /api/members/:id/set-password
Set member password
- Requires: Admin authentication
```json
{
  "password": "newpassword123"
}
```

---

## 🏘️ Cell Groups Endpoints

### GET /api/cell-groups
Get all cell groups
- Query params: `page`, `limit`, `search`, `district_id`
- Requires: Authentication

### GET /api/cell-groups/:id
Get cell group by ID with members and details
- Requires: Authentication

### POST /api/cell-groups
Create new cell group
- Requires: Admin authentication
```json
{
  "name": "Youth Cell Group",
  "description": "Cell group for young adults",
  "district_id": "uuid",
  "leader_id": "uuid",
  "assistant_leader_id": "uuid",
  "meeting_day": "friday",
  "meeting_time": "19:00",
  "meeting_location": "Church Hall A"
}
```

### PUT /api/cell-groups/:id
Update cell group
- Requires: Admin authentication

### DELETE /api/cell-groups/:id
Soft delete cell group
- Requires: Admin authentication

### GET /api/cell-groups/:id/members
Get cell group members
- Requires: Authentication

### POST /api/cell-groups/:id/members
Add members to cell group
- Requires: Admin authentication
```json
{
  "member_ids": ["uuid1", "uuid2", "uuid3"]
}
```

### DELETE /api/cell-groups/:id/members/:memberId
Remove member from cell group
- Requires: Admin authentication

### GET /api/cell-groups/:id/meetings
Get cell group meetings/attendance history
- Query params: `page`, `limit`
- Requires: Authentication

---

## 🗺️ Districts Endpoints

### GET /api/districts
Get all districts
- Query params: `page`, `limit`, `search`
- Requires: Authentication

### GET /api/districts/:id
Get district by ID with cell groups
- Requires: Authentication

### POST /api/districts
Create new district
- Requires: Admin authentication
```json
{
  "name": "North District",
  "description": "Northern area of the city",
  "leader1_id": "uuid",
  "leader2_id": "uuid"
}
```

### PUT /api/districts/:id
Update district
- Requires: Admin authentication

### DELETE /api/districts/:id
Soft delete district
- Requires: Admin authentication

### GET /api/districts/:id/cell-groups
Get district cell groups
- Requires: Authentication

### GET /api/districts/:id/members
Get district members
- Query params: `page`, `limit`
- Requires: Authentication

---

## 📊 Attendance Endpoints

### GET /api/attendance/meetings
Get all meetings with filters
- Query params: `page`, `limit`, `search`, `event_category`, `start_date`, `end_date`, `cell_group_id`, `ministry_id`
- Requires: Authentication

### GET /api/attendance/meetings/:id
Get meeting by ID with participants and visitors
- Requires: Authentication

### POST /api/attendance/meetings
Create meeting with attendance
- Requires: Admin authentication
```json
{
  "event_category": "cell_group",
  "meeting_date": "2024-01-15T19:00:00Z",
  "meeting_type": "Weekly Meeting",
  "topic": "Prayer and Fellowship",
  "notes": "Great meeting with good participation",
  "location": "Church Hall A",
  "offering": 150.50,
  "cell_group_id": "uuid",
  "ministry_id": null,
  "participants": [
    {
      "member_id": "uuid",
      "status": "present",
      "notes": "Active participation"
    },
    {
      "member_id": "uuid2",
      "status": "late",
      "notes": "Arrived 15 minutes late"
    }
  ],
  "visitors": [
    {
      "name": "Jane Smith",
      "phone": "+1234567890",
      "email": "<EMAIL>",
      "notes": "First time visitor"
    }
  ]
}
```

### PUT /api/attendance/meetings/:id
Update meeting details
- Requires: Admin authentication

### DELETE /api/attendance/meetings/:id
Delete meeting and all related attendance
- Requires: Admin authentication

### GET /api/attendance/meetings/:id/participants
Get meeting participants
- Requires: Authentication

### PUT /api/attendance/meetings/:id/participants
Update meeting participants
- Requires: Admin authentication
```json
{
  "participants": [
    {
      "member_id": "uuid",
      "status": "present",
      "notes": "Updated status"
    }
  ]
}
```

### GET /api/attendance/meetings/:id/visitors
Get meeting visitors
- Requires: Authentication

### POST /api/attendance/meetings/:id/visitors
Add visitors to meeting
- Requires: Admin authentication
```json
{
  "visitors": [
    {
      "name": "New Visitor",
      "phone": "+1234567890"
    }
  ]
}
```

### GET /api/attendance/stats
Get attendance statistics
- Query params: `start_date`, `end_date`, `event_category`
- Requires: Authentication

### GET /api/attendance/members/:memberId/history
Get member attendance history
- Query params: `page`, `limit`
- Requires: Authentication

### PATCH /api/attendance/meetings/:id/realtime
Enable/disable real-time for meeting
- Requires: Admin authentication
```json
{
  "is_realtime": true
}
```

---

## 📚 Classes Endpoints

### GET /api/classes
Get all classes
- Query params: `page`, `limit`, `search`, `category`, `status`
- Requires: Authentication

### GET /api/classes/:id
Get class by ID with levels and sessions
- Requires: Authentication

### POST /api/classes
Create new class
- Requires: Admin authentication
```json
{
  "name": "Bible Study 101",
  "description": "Introduction to Bible study",
  "category": "bible_study",
  "max_students": 20,
  "has_levels": true
}
```

### PUT /api/classes/:id
Update class
- Requires: Admin authentication

### DELETE /api/classes/:id
Delete class
- Requires: Admin authentication

### GET /api/classes/:id/levels
Get class levels
- Requires: Authentication

### POST /api/classes/:id/levels
Create class level
- Requires: Admin authentication
```json
{
  "name": "Level 1",
  "description": "Basic level",
  "order_number": 1,
  "prerequisite_level_id": null
}
```

### GET /api/classes/:id/sessions
Get class sessions
- Requires: Authentication

### POST /api/classes/:id/sessions
Create class session
- Requires: Admin authentication
```json
{
  "title": "Introduction Session",
  "description": "First session of the class",
  "session_date": "2024-01-20",
  "start_time": "10:00",
  "end_time": "12:00",
  "location": "Classroom A",
  "instructor_id": "uuid",
  "order_number": 1
}
```

### GET /api/classes/:id/enrollments
Get class enrollments
- Requires: Authentication

### POST /api/classes/:id/enrollments
Enroll member in class
- Requires: Admin authentication
```json
{
  "member_id": "uuid",
  "level_id": "uuid"
}
```

### PUT /api/classes/:id/enrollments/:enrollmentId
Update enrollment status
- Requires: Admin authentication
```json
{
  "status": "completed"
}
```

---

## ⛪ Ministries Endpoints

### GET /api/ministries
Get all ministries
- Query params: `page`, `limit`, `search`
- Requires: Authentication

### GET /api/ministries/:id
Get ministry by ID with members
- Requires: Authentication

### POST /api/ministries
Create new ministry
- Requires: Admin authentication
```json
{
  "name": "Worship Ministry",
  "description": "Leading worship services",
  "leader_id": "uuid"
}
```

### PUT /api/ministries/:id
Update ministry
- Requires: Admin authentication

### DELETE /api/ministries/:id
Soft delete ministry
- Requires: Admin authentication

### GET /api/ministries/:id/members
Get ministry members
- Requires: Authentication

### POST /api/ministries/:id/members
Add members to ministry
- Requires: Admin authentication
```json
{
  "member_ids": ["uuid1", "uuid2"],
  "role": "Musician"
}
```

### PUT /api/ministries/:id/members/:memberId
Update ministry member role
- Requires: Admin authentication
```json
{
  "role": "Lead Musician",
  "status": "active"
}
```

### DELETE /api/ministries/:id/members/:memberId
Remove member from ministry
- Requires: Admin authentication

### GET /api/ministries/:id/meetings
Get ministry meetings
- Query params: `page`, `limit`
- Requires: Authentication

---

## 📰 Articles Endpoints (Public + Admin)

### GET /api/articles
Get published articles (public) or all articles (admin)
- Query params: `page`, `limit`, `search`, `category`, `status`, `featured`
- Public endpoint (no auth required for published articles)

### GET /api/articles/:id
Get article by ID
- Public endpoint for published articles

### POST /api/articles
Create new article
- Requires: Admin authentication
```json
{
  "title": "Church News Update",
  "summary": "Latest updates from our church community",
  "content": "Full article content here...",
  "category": "news",
  "image_url": "https://example.com/image.jpg",
  "status": "published",
  "featured": true
}
```

### PUT /api/articles/:id
Update article
- Requires: Admin authentication

### DELETE /api/articles/:id
Delete article
- Requires: Admin authentication

### PATCH /api/articles/:id/publish
Publish article
- Requires: Admin authentication

### PATCH /api/articles/:id/unpublish
Unpublish article (set to draft)
- Requires: Admin authentication

### PATCH /api/articles/:id/featured
Toggle featured status
- Requires: Admin authentication
```json
{
  "featured": true
}
```

### POST /api/articles/:id/view
Increment article view count
- Public endpoint

### GET /api/articles/featured/list
Get featured articles
- Query params: `limit`
- Public endpoint

### GET /api/articles/category/:category
Get articles by category
- Query params: `page`, `limit`
- Public endpoint

### GET /api/articles/categories/list
Get all categories with article counts
- Public endpoint

---

## 🔄 Real-time Socket.IO Events

### Client Events (Emit):
- `join-meeting`: Join meeting room for real-time updates
- `leave-meeting`: Leave meeting room

### Server Events (Listen):
- `attendance-updated`: Attendance data changed
- `meeting-updated`: Meeting details changed

### Example Usage:
```javascript
// Join meeting room
socket.emit('join-meeting', meetingId);

// Listen for updates
socket.on('attendance-updated', (data) => {
  console.log('Attendance updated:', data);
  // Update UI with new attendance data
});
```

---

## 📋 Response Format

All API responses follow this format:

### Success Response:
```json
{
  "success": true,
  "data": { ... },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### Error Response:
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE"
  }
}
```

## 🔐 Authentication Headers

For protected endpoints, include JWT token:
```http
Authorization: Bearer <your-jwt-token>
```

## 📊 Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request / Validation Error
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error
