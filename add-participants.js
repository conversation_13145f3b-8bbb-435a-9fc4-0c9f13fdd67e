const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addParticipantsToExistingMeeting() {
    try {
        console.log('Adding participants to existing ministry meeting...\n');
        
        // Get existing ministry meeting
        const meeting = await prisma.attendanceMeeting.findFirst({
            where: { 
                event_category: 'ministry',
                ministry_id: { not: null }
            },
            include: {
                ministry: {
                    include: {
                        ministry_members: {
                            where: { status: 'active' },
                            include: {
                                member: true
                            }
                        }
                    }
                }
            }
        });
        
        if (!meeting) {
            console.log('No ministry meeting found!');
            return;
        }
        
        console.log(`Found meeting: ${meeting.topic} for ${meeting.ministry.name}`);
        console.log(`Ministry has ${meeting.ministry.ministry_members.length} active members`);
        
        // Check if participants already exist
        const existingParticipants = await prisma.attendanceParticipant.findMany({
            where: { meeting_id: meeting.id }
        });
        
        if (existingParticipants.length > 0) {
            console.log(`Meeting already has ${existingParticipants.length} participants. Skipping...`);
            return;
        }
        
        // Create participants for all ministry members
        const participantsData = meeting.ministry.ministry_members.map((mm, index) => ({
            meeting_id: meeting.id,
            member_id: mm.member.id,
            status: index < 2 ? 'present' : index < 4 ? 'late' : 'absent', // Mix of statuses for testing
            notes: null
        }));
        
        const result = await prisma.attendanceParticipant.createMany({
            data: participantsData
        });
        
        console.log(`Created ${result.count} participants for the meeting`);
        
        // Verify the count
        const updatedMeeting = await prisma.attendanceMeeting.findUnique({
            where: { id: meeting.id },
            select: {
                _count: {
                    select: {
                        participants: true
                    }
                }
            }
        });
        
        console.log(`Meeting now has ${updatedMeeting._count.participants} total participants`);
        
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await prisma.$disconnect();
    }
}

addParticipantsToExistingMeeting();
