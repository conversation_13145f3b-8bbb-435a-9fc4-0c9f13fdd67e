# Production Environment Configuration
NODE_ENV=production

# API Configuration - use localhost for internal communication
API_URL=http://localhost:3001

# Public URLs
NEXT_PUBLIC_API_URL=https://church-management.your-domain.com/api
NEXT_PUBLIC_FRONTEND_URL=https://church-management.your-domain.com

# Database Configuration (will be set by backend)
# DATABASE_URL will be configured in backend .env

# Security
NEXTAUTH_SECRET=your-super-secure-secret-key-here
NEXTAUTH_URL=https://church-management.your-domain.com
