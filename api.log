
> church-management-api@1.0.0 dev
> nodemon src/index.ts

[33m[nodemon] 3.1.10[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: ts,json[39m
[32m[nodemon] starting `ts-node src/index.ts`[39m
[dotenv@17.2.1] injecting env (6) from .env -- tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild
🚀 Server running on http://0.0.0.0:3001
📊 Environment: development
🔗 Frontend URL: http://localhost:3000
🌐 Accessible from network: http://*************:3001
node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: read EIO
    at TTY.onStreamRead (node:internal/stream_base_commons:216:20)
Emitted 'error' event on ReadStream instance at:
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  errno: -5,
  code: 'EIO',
  syscall: 'read'
}

Node.js v22.14.0
