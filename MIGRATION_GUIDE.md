# Migrasi Data dari Supabase ke MySQL

Script ini membantu Anda untuk migrasi data dari Supabase ke database MySQL lokal menggunakan Prisma.

## Prerequisites

1. Pastikan database MySQL sudah running
2. Pastikan Prisma schema sudah ter-generate
3. Pastikan database sudah ter-migrate dengan schema terbaru
4. Dapatkan Supabase URL dan Service Role Key

## Persiapan

1. Generate Prisma client terlebih dahulu:
   ```bash
   npm run db:generate
   ```

2. Pastikan database schema sudah up-to-date:
   ```bash
   npm run db:push
   ```

## Cara Menggunakan

### Metode 1: Menggunakan npm script (Recommended)

```bash
npm run migrate:supabase <SUPABASE_URL> <SUPABASE_SERVICE_KEY>
```

Contoh:
```bash
npm run migrate:supabase https://your-project.supabase.co eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Metode 2: Menjalankan script langsung

```bash
node scripts/migrate-data.js <SUPABASE_URL> <SUPABASE_SERVICE_KEY>
```

## Data yang Dimigrasikan

Script ini akan memigrasikan data untuk entitas berikut dengan urutan yang benar untuk menghindari masalah foreign key:

1. **Districts** - Data distrik/wilayah
2. **Members** - Data anggota gereja 
3. **Cell Groups** - Data kelompok sel
4. **Cell Group Members** - Data keanggotaan kelompok sel
5. **Attendance Meetings** - Data pertemuan absensi
6. **Attendance Participants** - Data peserta absensi
7. **Attendance Visitors** - Data pengunjung

## Mendapatkan Supabase Credentials

### Supabase URL
1. Login ke dashboard Supabase
2. Pilih project Anda
3. Pergi ke Settings → API
4. Copy "Project URL"

### Service Role Key
1. Di halaman yang sama (Settings → API)
2. Copy "service_role" key di bagian "Project API keys"
3. **PENTING:** Jangan pernah share key ini atau commit ke git!

## Output

Script akan menampilkan:
- Progress migrasi untuk setiap entitas
- Jumlah record yang berhasil dimigrasikan
- Jumlah error yang terjadi
- Summary lengkap di akhir proses

## Fitur Script

- **Upsert Strategy**: Menggunakan upsert sehingga aman untuk dijalankan berulang kali
- **Error Handling**: Melanjutkan proses meski ada error pada record tertentu
- **Progress Tracking**: Menampilkan progress real-time
- **Connection Testing**: Memverifikasi koneksi sebelum memulai migrasi
- **Data Validation**: Melakukan parsing dan validasi data sebelum insert

## Troubleshooting

### Error: "Connection failed"
- Pastikan Supabase URL dan Service Key benar
- Pastikan MySQL server running
- Cek konfigurasi DATABASE_URL di .env

### Error: "Foreign key constraint"
- Script sudah diatur dengan urutan yang benar
- Pastikan tidak ada data yang tidak konsisten di Supabase

### Error: "Duplicate entry"
- Script menggunakan upsert, jadi error ini seharusnya tidak terjadi
- Jika terjadi, berarti ada masalah dengan constraint unique

### Error: "Field validation failed"
- Ada data di Supabase yang tidak sesuai dengan schema MySQL
- Cek log error untuk detail field yang bermasalah

## Backup

Sebelum menjalankan migrasi, sebaiknya backup database MySQL Anda:

```bash
mysqldump -u username -p database_name > backup.sql
```

## Restore (jika diperlukan)

Jika perlu restore backup:

```bash
mysql -u username -p database_name < backup.sql
```

## Environment Variables

Pastikan .env file berisi:

```
DATABASE_URL="mysql://username:password@localhost:3306/database_name"
```

## Support

Jika mengalami masalah, cek:
1. Log error yang ditampilkan script
2. Pastikan semua prerequisites terpenuhi
3. Verify data di Supabase tidak corrupt
4. Cek constraint dan schema di MySQL
