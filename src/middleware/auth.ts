import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { config } from "../config/config";
import { prisma } from "../config/database";
import { createError } from "./errorHandler";

export interface AuthRequest extends Request {
    user?: {
        id: string;
        email: string;
        role: string;
        role_level: number;
    };
}

export const authenticateToken = async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(" ")[1]; // Bearer TOKEN

        console.log("🔐 Auth check:", {
            hasAuthHeader: !!authHeader,
            tokenLength: token?.length,
            path: req.path,
            method: req.method,
        });

        if (!token) {
            console.log("❌ No token provided");
            throw createError("Access token required", 401);
        }

        const decoded = jwt.verify(token, config.JWT_SECRET) as any;
        console.log("✅ Token decoded:", {
            userId: decoded.id,
            email: decoded.email,
        });

        // Get user from database to ensure they still exist and get latest info
        const user = await prisma.member.findUnique({
            where: { id: decoded.id },
            select: {
                id: true,
                email: true,
                role: true,
                role_level: true,
                status: true,
            },
        });

        if (!user || user.status !== "active") {
            console.log("❌ User not found or inactive:", {
                userId: decoded.id,
            });
            throw createError("User not found or inactive", 401);
        }

        console.log("✅ Auth successful:", {
            userId: user.id,
            role: user.role,
        });
        req.user = user;
        next();
    } catch (error) {
        console.error(
            "❌ Auth error:",
            error instanceof Error ? error.message : error
        );
        if (error instanceof jwt.JsonWebTokenError) {
            next(createError("Invalid token", 401));
        } else {
            next(error);
        }
    }
};

export const requireAdmin = (
    req: AuthRequest,
    res: Response,
    next: NextFunction
) => {
    if (!req.user) {
        return next(createError("Authentication required", 401));
    }

    if (req.user.role !== "admin" && req.user.role_level < 4) {
        return next(createError("Admin access required", 403));
    }

    next();
};

export const requireMember = (
    req: AuthRequest,
    res: Response,
    next: NextFunction
) => {
    if (!req.user) {
        return next(createError("Authentication required", 401));
    }

    next();
};
