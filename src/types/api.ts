export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: {
        message: string;
        code?: string;
    };
    pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}

export interface PaginationQuery {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
}

export interface LoginRequest {
    email: string;
    password: string;
}

export interface LoginResponse {
    user: {
        id: string;
        email: string;
        first_name: string;
        last_name: string;
        role: string;
        role_level: number;
    };
    tokens: {
        access_token: string;
        refresh_token: string;
    };
}

export interface MemberCreateRequest {
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
    address?: string;
    date_of_birth?: string;
    gender?: string;
    marital_status?: string;
    emergency_contact_name?: string;
    emergency_contact_phone?: string;
    notes?: string;
    cell_group_id?: string;
    district_id?: string;
}

export interface AttendanceMeetingRequest {
    event_category: string;
    meeting_date: string;
    meeting_type: string;
    topic?: string;
    notes?: string;
    location?: string;
    offering?: number;
    cell_group_id?: string;
    ministry_id?: string;
    participants: Array<{
        member_id: string;
        status: "present" | "absent" | "late";
        notes?: string;
    }>;
    visitors?: Array<{
        first_name: string;
        last_name: string;
        name?: string; // For backward compatibility
        phone?: string;
        email?: string;
        notes?: string;
    }>;
}
