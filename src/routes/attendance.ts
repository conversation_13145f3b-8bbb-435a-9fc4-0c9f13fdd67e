import { Router } from "express";
import { body, param, query } from "express-validator";
import { AttendanceController } from "../controllers/AttendanceController";
import { authenticateToken, requireAdmin } from "../middleware/auth";

const router = Router();
const attendanceController = new AttendanceController();

// Get all meetings (with pagination and filters)
router.get("/meetings", authenticateToken, attendanceController.getMeetings);

// Get meeting by ID
router.get(
    "/meetings/:id",
    [authenticateToken, param("id").isUUID()],
    attendanceController.getMeetingById
);

// Create new meeting with attendance
router.post(
    "/meetings",
    [
        authenticateToken,
        requireAdmin,
        body("event_category").isIn([
            "cell_group",
            "ministry",
            "sunday_service",
            "special_event",
            "prayer",
            "class",
            "other",
        ]),
        body("meeting_date").isISO8601(),
        body("meeting_type").isLength({ min: 1 }).trim(),
        body("topic").isLength({ min: 1 }).trim(),
        body("notes").optional({ nullable: true }).trim(),
        body("location").isLength({ min: 1 }).trim(),
        body("offering").optional({ nullable: true }).isFloat({ min: 0 }),
        body("cell_group_id").optional({ nullable: true }).isUUID(),
        body("ministry_id").optional({ nullable: true }).isUUID(),
        body("participants").optional({ nullable: true }).isArray(),
        body("participants.*.member_id").isUUID(),
        body("participants.*.status").isIn([
            "present",
            "absent",
            "late",
            "excused",
        ]),
        body("visitors").optional({ nullable: true }).isArray(),
        body("visitors.*.name").isLength({ min: 1 }).trim(),
    ],
    attendanceController.createMeeting
);

// Update meeting
router.put(
    "/meetings/:id",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("event_category")
            .optional()
            .isIn([
                "cell_group",
                "ministry",
                "sunday_service",
                "special_event",
                "prayer",
                "class",
                "other",
            ]),
        body("meeting_date").optional().isISO8601(),
        body("meeting_type").optional().isLength({ min: 1 }).trim(),
        body("topic").optional().trim(),
        body("notes").optional({ nullable: true }).trim(),
        body("location").optional().trim(),
        body("offering").optional({ nullable: true }).isFloat({ min: 0 }),
        body("cell_group_id").optional({ nullable: true }).isUUID(),
        body("ministry_id").optional({ nullable: true }).isUUID(),
        body("participants").optional({ nullable: true }).isArray(),
        body("participants.*.member_id").optional().isUUID(),
        body("participants.*.status")
            .optional()
            .isIn(["present", "absent", "late", "excused"]),
        body("visitors").optional({ nullable: true }).isArray(),
        body("visitors.*.name").optional().isLength({ min: 1 }),
    ],
    attendanceController.updateMeeting
);

// Delete meeting
router.delete(
    "/meetings/:id",
    [authenticateToken, requireAdmin, param("id").isUUID()],
    attendanceController.deleteMeeting
);

// Get meeting participants
router.get(
    "/meetings/:id/participants",
    [authenticateToken, param("id").isUUID()],
    attendanceController.getMeetingParticipants
);

// Update meeting participants
router.put(
    "/meetings/:id/participants",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("participants").isArray(),
        body("participants.*.member_id").isUUID(),
        body("participants.*.status").isIn([
            "present",
            "absent",
            "late",
            "excused",
        ]),
    ],
    attendanceController.updateMeetingParticipants
);

// Get meeting visitors
router.get(
    "/meetings/:id/visitors",
    [authenticateToken, param("id").isUUID()],
    attendanceController.getMeetingVisitors
);

// Add visitors to meeting
router.post(
    "/meetings/:id/visitors",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("visitors").isArray(),
        body("visitors.*.name").isLength({ min: 1 }).trim(),
    ],
    attendanceController.addMeetingVisitors
);

// Get attendance statistics
router.get(
    "/stats",
    [
        authenticateToken,
        query("start_date").optional().isISO8601(),
        query("end_date").optional().isISO8601(),
        query("event_category")
            .optional()
            .isIn([
                "cell_group",
                "ministry",
                "service",
                "prayer",
                "class",
                "other",
            ]),
    ],
    attendanceController.getAttendanceStats
);

// Get member attendance history with time filter
router.get(
    "/members/:memberId/attendance",
    [
        authenticateToken,
        param("memberId").isUUID(),
        query("timeFilter")
            .optional()
            .isIn(["all", "month", "quarter", "year"]),
    ],
    attendanceController.getMemberAttendance
);

// Get member attendance history (legacy endpoint)
router.get(
    "/members/:memberId/history",
    [authenticateToken, param("memberId").isUUID()],
    attendanceController.getMemberAttendanceHistory
);

// Enable/disable real-time for meeting
router.patch(
    "/meetings/:id/realtime",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("is_realtime").isBoolean(),
    ],
    attendanceController.toggleMeetingRealtime
);

// Convert visitor to member
router.post(
    "/visitors/:id/convert-to-member",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("cell_group_id").optional({ nullable: true }).isUUID(),
        body("district_id").optional({ nullable: true }).isUUID(),
        body("baptism_date").optional({ nullable: true }).isISO8601(),
        body("join_date").optional({ nullable: true }).isISO8601(),
        body("additional_info").optional({ nullable: true }).trim(),
    ],
    attendanceController.convertVisitorToMember
);

// Get converted visitors
router.get(
    "/visitors/converted",
    [
        authenticateToken,
        query("page").optional().isInt({ min: 1 }),
        query("limit").optional().isInt({ min: 1, max: 100 }),
    ],
    attendanceController.getConvertedVisitors
);

// Live Attendance Routes
// Toggle live attendance for a meeting (admin only)
router.patch(
    "/meetings/:id/live-attendance",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("active").isBoolean(),
        body("expires_at").optional({ nullable: true }).isISO8601(),
    ],
    attendanceController.toggleLiveAttendance
);

// Get live attendance status for a meeting
router.get(
    "/meetings/:id/live-status",
    [authenticateToken, param("id").isUUID()],
    attendanceController.getLiveAttendanceStatus
);

// Member live check-in (for QR code scanning)
router.post(
    "/meetings/:id/live-checkin",
    [authenticateToken, param("id").isUUID(), body("member_id").isUUID()],
    attendanceController.liveCheckin
);

export default router;
