import { Router } from "express";
import { authenticateToken, requireAdmin } from "../middleware/auth";
import {
    getProjects,
    getProject,
    createProject,
    updateProject,
    deleteProject,
    getPublishedProjects,
} from "../controllers/ProjectController";

const router = Router();

// Public routes (for members to view published projects)
router.get("/published", getPublishedProjects); // Get published projects only

// Protected routes (require authentication)
router.use(authenticateToken);

// Routes accessible by all authenticated users
router.get("/", getProjects); // Get all projects (admin can see all, members see published only)
router.get("/:id", getProject); // Get single project

// Admin-only routes
router.use(requireAdmin);
router.post("/", createProject); // Create new project
router.put("/:id", updateProject); // Update project
router.delete("/:id", deleteProject); // Delete project

export default router;
