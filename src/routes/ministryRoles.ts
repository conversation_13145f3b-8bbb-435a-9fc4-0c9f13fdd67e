import express from "express";
import { body } from "express-validator";
import { authenticateToken, requireAdmin } from "../middleware/auth";
import { MinistryRoleController } from "../controllers/MinistryRoleController";

const router = express.Router();
const ministryRoleController = new MinistryRoleController();

// Validation rules for ministry roles
const createMinistryRoleValidation = [
    body("name")
        .notEmpty()
        .withMessage("Role name is required")
        .isLength({ min: 2, max: 100 })
        .withMessage("Role name must be between 2 and 100 characters"),
    body("description")
        .optional()
        .isLength({ max: 500 })
        .withMessage("Description cannot exceed 500 characters"),
    body("ministry_type")
        .optional()
        .isIn(["worship", "media", "prayer", "general", "hospitality"])
        .withMessage(
            "Ministry type must be one of: worship, media, prayer, general, hospitality"
        ),
    body("is_leadership")
        .optional()
        .isBoolean()
        .withMessage("is_leadership must be a boolean value"),
];

const updateMinistryRoleValidation = [
    body("name")
        .optional()
        .isLength({ min: 2, max: 100 })
        .withMessage("Role name must be between 2 and 100 characters"),
    body("description")
        .optional()
        .isLength({ max: 500 })
        .withMessage("Description cannot exceed 500 characters"),
    body("ministry_type")
        .optional()
        .isIn(["worship", "media", "prayer", "general", "hospitality"])
        .withMessage(
            "Ministry type must be one of: worship, media, prayer, general, hospitality"
        ),
    body("is_leadership")
        .optional()
        .isBoolean()
        .withMessage("is_leadership must be a boolean value"),
];

// Routes
router.get("/", authenticateToken, ministryRoleController.getMinistryRoles);
router.post(
    "/",
    authenticateToken,
    requireAdmin,
    createMinistryRoleValidation,
    ministryRoleController.createMinistryRole
);
router.put(
    "/:id",
    authenticateToken,
    requireAdmin,
    updateMinistryRoleValidation,
    ministryRoleController.updateMinistryRole
);
router.delete(
    "/:id",
    authenticateToken,
    requireAdmin,
    ministryRoleController.deleteMinistryRole
);

export default router;
