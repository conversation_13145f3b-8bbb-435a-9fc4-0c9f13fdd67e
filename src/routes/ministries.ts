import { Router } from 'express';
import { body, param } from 'express-validator';
import { MinistryController } from '../controllers/MinistryController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();
const ministryController = new MinistryController();

// Get all ministries
router.get('/', authenticateToken, ministryController.getMinistries);

// Get ministry by ID
router.get('/:id', [
  authenticateToken,
  param('id').isUUID(),
], ministryController.getMinistryById);

// Create new ministry
router.post('/', [
  authenticateToken,
  requireAdmin,
  body('name').isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('leader_id').optional().isUUID(),
], ministryController.createMinistry);

// Update ministry
router.put('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], ministryController.updateMinistry);

// Delete ministry
router.delete('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], ministryController.deleteMinistry);

// Get ministry members
router.get('/:id/members', [
  authenticateToken,
  param('id').isUUID(),
], ministryController.getMinistryMembers);

// Add members to ministry
router.post('/:id/members', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('member_ids').isArray(),
  body('member_ids.*').isUUID(),
  body('role').optional().trim(),
], ministryController.addMembersToMinistry);

// Update ministry member role
router.put('/:id/members/:memberId', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  param('memberId').isUUID(),
  body('role').optional().trim(),
  body('status').optional().isIn(['active', 'inactive']),
], ministryController.updateMinistryMember);

// Remove member from ministry
router.delete('/:id/members/:memberId', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  param('memberId').isUUID(),
], ministryController.removeMemberFromMinistry);

// Get ministry meetings/attendance
router.get('/:id/meetings', [
  authenticateToken,
  param('id').isUUID(),
], ministryController.getMinistryMeetings);

export default router;
