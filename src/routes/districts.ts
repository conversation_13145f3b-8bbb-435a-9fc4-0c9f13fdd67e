import { Router } from 'express';
import { body, param } from 'express-validator';
import { DistrictController } from '../controllers/DistrictController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();
const districtController = new DistrictController();

// Get all districts
router.get('/', authenticateToken, districtController.getDistricts);

// Get district by ID
router.get('/:id', [
  authenticateToken,
  param('id').isUUID(),
], districtController.getDistrictById);

// Create new district
router.post('/', [
  authenticateToken,
  requireAdmin,
  body('name').isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('leader1_id').optional().isUUID(),
  body('leader2_id').optional().isUUID(),
], districtController.createDistrict);

// Update district
router.put('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('name').optional().isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('leader1_id').optional().isUUID(),
  body('leader2_id').optional().isUUID(),
], districtController.updateDistrict);

// Delete district
router.delete('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], districtController.deleteDistrict);

// Get district cell groups
router.get('/:id/cell-groups', [
  authenticateToken,
  param('id').isUUID(),
], districtController.getDistrictCellGroups);

// Get district members
router.get('/:id/members', [
  authenticateToken,
  param('id').isUUID(),
], districtController.getDistrictMembers);

export default router;
