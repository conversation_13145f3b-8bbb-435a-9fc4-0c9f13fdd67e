import { Router } from "express";
import { body, param, query } from "express-validator";
import { authenticateToken } from "../middleware/auth";
import { ClassEnrollmentController } from "../controllers/ClassEnrollmentController";

const router = Router();
const enrollmentController = new ClassEnrollmentController();

// Search members for enrollment
router.get(
    "/classes/:classId/search-members",
    [
        authenticateToken,
        param("classId").notEmpty().withMessage("Invalid class ID"),
        query("q")
            .isLength({ min: 2 })
            .withMessage("Search query must be at least 2 characters"),
        query("limit")
            .optional()
            .isInt({ min: 1, max: 50 })
            .withMessage("Limit must be between 1 and 50"),
        query("exclude_enrolled")
            .optional()
            .isBoolean()
            .withMessage("exclude_enrolled must be boolean"),
    ],
    enrollmentController.searchMembers
);

// Batch enroll members
router.post(
    "/classes/:classId/batch-enroll",
    [
        authenticateToken,
        param("classId").notEmpty().withMessage("Invalid class ID"),
        body("member_ids")
            .isArray({ min: 1 })
            .withMessage("member_ids must be a non-empty array"),
        body("member_ids").custom((value) => {
            if (!Array.isArray(value)) {
                throw new Error("member_ids must be an array");
            }
            for (const id of value) {
                if (typeof id !== "string" || id.trim().length === 0) {
                    throw new Error(
                        "Each member_id must be a non-empty string"
                    );
                }
            }
            return true;
        }),
        body("level_id")
            .optional()
            .notEmpty()
            .withMessage("level_id must be a non-empty string"),
    ],
    enrollmentController.batchEnroll
);

// Get enrollment progress
router.get(
    "/enrollments/:enrollmentId/progress",
    [
        authenticateToken,
        param("enrollmentId").notEmpty().withMessage("Invalid enrollment ID"),
    ],
    enrollmentController.getEnrollmentProgress
);

// Promote member to next level
router.post(
    "/enrollments/:enrollmentId/promote",
    [
        authenticateToken,
        param("enrollmentId").notEmpty().withMessage("Invalid enrollment ID"),
        body("notes")
            .optional()
            .isString()
            .withMessage("Notes must be a string"),
    ],
    enrollmentController.promoteToNextLevel
);

export default router;
