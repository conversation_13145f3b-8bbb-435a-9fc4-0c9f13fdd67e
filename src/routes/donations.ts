import { Router } from 'express';
import { authenticateToken, requireAdmin } from '../middleware/auth';
import {
  getDonations,
  getDonation,
  createDonation,
  updateDonation,
  deleteDonation,
  getDonationsByProject
} from '../controllers/DonationController';

const router = Router();

// Public routes (for creating donations)
router.post('/', createDonation); // Create new donation (public for donation form)
router.get('/project/:project_id', getDonationsByProject); // Get donations for a project (public for display)

// Protected routes (require authentication)
router.use(authenticateToken);

// Admin-only routes
router.use(requireAdmin);
router.get('/', getDonations); // Get all donations (admin only)
router.get('/:id', getDonation); // Get single donation (admin only)
router.put('/:id', updateDonation); // Update donation (admin only)
router.delete('/:id', deleteDonation); // Delete donation (admin only)

export default router;
