import { Router } from "express";
import { body, param } from "express-validator";
import { ArticleCategoryController } from "../controllers/ArticleCategoryController";
import { authenticateToken, requireAdmin } from "../middleware/auth";

const router = Router();
const categoryController = new ArticleCategoryController();

// Get all categories (public endpoint)
router.get("/", categoryController.getCategories);

// Create new category
router.post(
    "/",
    [
        authenticateToken,
        requireAdmin,
        body("name").isLength({ min: 1 }).trim(),
        body("description").optional().trim(),
        body("icon").optional().trim(),
    ],
    categoryController.createCategory
);

// Update category
router.put(
    "/:id",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("name").isLength({ min: 1 }).trim(),
        body("description").optional().trim(),
        body("icon").optional().trim(),
    ],
    categoryController.updateCategory
);

// Delete category
router.delete(
    "/:id",
    [authenticateToken, requireAdmin, param("id").isUUID()],
    categoryController.deleteCategory
);

export default router;
