import { Router } from "express";
import { body, param, query } from "express-validator";
import { MemberController } from "../controllers/MemberController";
import { authenticateToken, requireAdmin } from "../middleware/auth";

const router = Router();
const memberController = new MemberController();

// Get all members (with pagination and search)
router.get("/", authenticateToken, memberController.getMembers);

// Get member by ID
router.get(
    "/:id",
    [authenticateToken, param("id").isUUID()],
    memberController.getMemberById
);

// Create new member
router.post(
    "/",
    [
        authenticateToken,
        requireAdmin,
        body("email").isEmail().normalizeEmail(),
        body("first_name").isLength({ min: 1 }).trim(),
        body("last_name").isLength({ min: 1 }).trim(),
        body("phone").optional().isMobilePhone("any"),
        body("date_of_birth").optional().isISO8601(),
        body("gender").optional().isIn(["male", "female"]),
        body("marital_status")
            .optional()
            .isIn(["single", "married", "divorced", "widowed"]),
    ],
    memberController.createMember
);

// Update member
router.put(
    "/:id",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("email").optional().isEmail().normalizeEmail(),
        body("first_name").optional().isLength({ min: 1 }).trim(),
        body("last_name").optional().isLength({ min: 1 }).trim(),
        body("phone").optional().isMobilePhone("any"),
        body("date_of_birth").optional().isISO8601(),
        body("gender").optional().isIn(["male", "female"]),
        body("marital_status")
            .optional()
            .isIn(["single", "married", "divorced", "widowed"]),
    ],
    memberController.updateMember
);

// Delete member
router.delete(
    "/:id",
    [authenticateToken, requireAdmin, param("id").isUUID()],
    memberController.deleteMember
);

// Get member attendance history
router.get(
    "/:id/attendance",
    [authenticateToken, param("id").isUUID()],
    memberController.getMemberAttendance
);

// Set member password
router.post(
    "/:id/set-password",
    [
        authenticateToken,
        requireAdmin,
        param("id").isUUID(),
        body("password").isLength({ min: 8 }),
    ],
    memberController.setMemberPassword
);

// Member self-update profile (limited fields)
router.put(
    "/:id/profile",
    [
        authenticateToken,
        param("id").isUUID(),
        body("first_name").optional().isLength({ min: 1 }).trim(),
        body("last_name").optional().isLength({ min: 1 }).trim(),
        body("phone").optional().isMobilePhone("any"),
        body("address").optional().isString(),
        body("date_of_birth").optional().isISO8601(),
        body("gender").optional().isIn(["male", "female"]),
        body("marital_status")
            .optional()
            .isIn(["single", "married", "divorced", "widowed"]),
        body("occupation").optional().isString(),
        body("emergency_contact_name").optional().isString(),
        body("emergency_contact_phone").optional().isMobilePhone("any"),
    ],
    memberController.updateMemberProfile
);

export default router;
