import bcrypt from 'bcryptjs';
import { config } from '../config/config';

export const hashPassword = async (password: string): Promise<string> => {
  return bcrypt.hash(password, config.BCRYPT_ROUNDS);
};

export const comparePassword = async (
  password: string,
  hashedPassword: string
): Promise<boolean> => {
  return bcrypt.compare(password, hashedPassword);
};

export const generateDefaultPassword = (dateOfBirth?: Date | string): string => {
  if (!dateOfBirth) {
    return 'church123'; // Default fallback
  }
  
  const date = new Date(dateOfBirth);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  return `${day}${month}${year}`; // Format: DDMMYYYY
};
