import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { AuthRequest } from "../middleware/auth";
import { ApiResponse } from "../types/api";

export class MinistryRoleController {
    // Static method to auto-create role if it doesn't exist
    static async autoCreateRoleIfNeeded(roleName: string): Promise<void> {
        if (!roleName || typeof roleName !== "string" || !roleName.trim()) {
            return;
        }

        const trimmedRole = roleName.trim();

        try {
            const roleExists = await prisma.ministryRole.findFirst({
                where: {
                    name: trimmedRole,
                    status: "active",
                },
            });

            if (!roleExists) {
                await prisma.ministryRole.create({
                    data: {
                        name: trimmedRole,
                        description: `Auto-created role: ${trimmedRole}`,
                        ministry_type: "general",
                        is_leadership: false,
                        sort_order: 999,
                    },
                });
                console.log(`Auto-created new ministry role: ${trimmedRole}`);
            }
        } catch (error) {
            console.log(`Failed to auto-create role ${trimmedRole}:`, error);
        }
    }

    async getMinistryRoles(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { ministry_type, is_leadership } = req.query;

            const where: any = { status: "active" };

            if (ministry_type) {
                where.ministry_type = ministry_type as string;
            }

            if (is_leadership !== undefined) {
                where.is_leadership = is_leadership === "true";
            }

            const ministryRoles = await prisma.ministryRole.findMany({
                where,
                select: {
                    id: true,
                    name: true,
                    description: true,
                    ministry_type: true,
                    is_leadership: true,
                    sort_order: true,
                },
                orderBy: [
                    { ministry_type: "asc" },
                    { sort_order: "asc" },
                    { name: "asc" },
                ],
            });

            res.json({
                success: true,
                data: ministryRoles,
            });
        } catch (error) {
            console.error("Error fetching ministry roles:", error);
            next(createError("Failed to fetch ministry roles", 500));
        }
    }

    async createMinistryRole(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return next(createError("Validation failed", 400));
            }

            const { name, description, ministry_type, is_leadership } =
                req.body;

            // Check if role name already exists
            const existingRole = await prisma.ministryRole.findFirst({
                where: { name, status: "active" },
            });

            if (existingRole) {
                return next(
                    createError("Role with this name already exists", 400)
                );
            }

            const newRole = await prisma.ministryRole.create({
                data: {
                    name,
                    description,
                    ministry_type,
                    is_leadership: is_leadership || false,
                },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    ministry_type: true,
                    is_leadership: true,
                },
            });

            res.status(201).json({
                success: true,
                data: newRole,
                message: "Ministry role created successfully",
            });
        } catch (error) {
            console.error("Error creating ministry role:", error);
            next(createError("Failed to create ministry role", 500));
        }
    }

    async updateMinistryRole(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return next(createError("Validation failed", 400));
            }

            const { id } = req.params;
            const { name, description, ministry_type, is_leadership } =
                req.body;

            // Check if role exists
            const existingRole = await prisma.ministryRole.findFirst({
                where: { id, status: "active" },
            });

            if (!existingRole) {
                return next(createError("Ministry role not found", 404));
            }

            // Check if name conflicts with other roles (excluding current one)
            if (name && name !== existingRole.name) {
                const nameConflict = await prisma.ministryRole.findFirst({
                    where: { name, status: "active", id: { not: id } },
                });

                if (nameConflict) {
                    return next(
                        createError("Role with this name already exists", 400)
                    );
                }
            }

            const updatedRole = await prisma.ministryRole.update({
                where: { id },
                data: {
                    name,
                    description,
                    ministry_type,
                    is_leadership,
                    updated_at: new Date(),
                },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    ministry_type: true,
                    is_leadership: true,
                },
            });

            res.json({
                success: true,
                data: updatedRole,
                message: "Ministry role updated successfully",
            });
        } catch (error) {
            console.error("Error updating ministry role:", error);
            next(createError("Failed to update ministry role", 500));
        }
    }

    async deleteMinistryRole(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;

            // Check if role exists
            const existingRole = await prisma.ministryRole.findFirst({
                where: { id, status: "active" },
            });

            if (!existingRole) {
                return next(createError("Ministry role not found", 404));
            }

            // Check if role is being used by any ministry members
            const roleInUse = await prisma.ministryMember.findFirst({
                where: { role: existingRole.name, status: "active" },
            });

            if (roleInUse) {
                return next(
                    createError(
                        "Cannot delete role that is currently being used by ministry members",
                        400
                    )
                );
            }

            // Soft delete by updating status
            await prisma.ministryRole.update({
                where: { id },
                data: {
                    status: "inactive",
                    updated_at: new Date(),
                },
            });

            res.json({
                success: true,
                message: "Ministry role deleted successfully",
            });
        } catch (error) {
            console.error("Error deleting ministry role:", error);
            next(createError("Failed to delete ministry role", 500));
        }
    }
}
