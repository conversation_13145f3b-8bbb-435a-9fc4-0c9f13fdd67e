import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { AuthRequest } from "../middleware/auth";
import { ApiResponse, PaginationQuery } from "../types/api";

export class ClassEnrollmentController {
    // Search members for enrollment
    async searchMembers(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            console.log("🔍 Search members request:", {
                query: req.query,
                params: req.params,
                path: req.path,
            });

            const { q, limit = 10, exclude_enrolled } = req.query;
            const { classId } = req.params;

            if (!q || typeof q !== "string" || q.trim().length < 2) {
                console.log("❌ Invalid search query:", q);
                throw createError(
                    "Search query must be at least 2 characters",
                    400
                );
            }

            const searchQuery = q.trim();
            const searchLimit = Math.min(Number(limit), 50); // Max 50 results

            console.log("🔍 Searching for:", searchQuery, "Class:", classId);

            // Base where condition for search
            const where: any = {
                status: "active",
                OR: [
                    {
                        first_name: {
                            contains: searchQuery,
                        },
                    },
                    {
                        last_name: {
                            contains: searchQuery,
                        },
                    },
                    {
                        email: {
                            contains: searchQuery,
                        },
                    },
                ],
            };

            // Exclude already enrolled members if requested
            if (exclude_enrolled === "true" && classId) {
                console.log("📋 Excluding already enrolled members");
                const enrolledMemberIds = await prisma.classEnrollment.findMany(
                    {
                        where: { class_id: classId },
                        select: { member_id: true },
                    }
                );

                console.log("👥 Already enrolled:", enrolledMemberIds.length);

                where.NOT = {
                    id: { in: enrolledMemberIds.map((e) => e.member_id) },
                };
            }

            const members = await prisma.member.findMany({
                where,
                select: {
                    id: true,
                    first_name: true,
                    last_name: true,
                    email: true,
                    phone: true,
                    cell_group: {
                        select: { id: true, name: true },
                    },
                    district: {
                        select: { id: true, name: true },
                    },
                },
                take: searchLimit,
                orderBy: [{ first_name: "asc" }, { last_name: "asc" }],
            });

            console.log("✅ Found members:", members.length);
            res.json({ success: true, data: members });
        } catch (error) {
            console.error("❌ Search members error:", error);
            next(error);
        }
    }

    // Batch enrollment of multiple members
    async batchEnroll(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            console.log("📝 Batch enroll request:", {
                params: req.params,
                body: req.body,
            });

            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                console.log("❌ Validation errors:", errors.array());
                throw createError("Validation failed", 400);
            }

            const { classId } = req.params;
            const { member_ids, level_id } = req.body;

            if (!Array.isArray(member_ids) || member_ids.length === 0) {
                throw createError("member_ids must be a non-empty array", 400);
            }

            // Verify class exists and get its details
            const classInfo = await prisma.class.findUnique({
                where: { id: classId },
                include: { levels: { orderBy: { order_number: "asc" } } },
            });

            if (!classInfo) {
                throw createError("Class not found", 404);
            }

            // For multi-level classes, determine the appropriate level
            let targetLevelId = level_id;
            if (classInfo.has_levels && !targetLevelId) {
                // Default to first level if no specific level provided
                const firstLevel = classInfo.levels[0];
                if (firstLevel) {
                    targetLevelId = firstLevel.id;
                } else {
                    throw createError(
                        "Class has levels but no levels found",
                        400
                    );
                }
            }

            // Check for already enrolled members
            const existingEnrollments = await prisma.classEnrollment.findMany({
                where: {
                    class_id: classId,
                    member_id: { in: member_ids },
                },
                select: { member_id: true },
            });

            const alreadyEnrolledIds = existingEnrollments.map(
                (e) => e.member_id
            );
            const newMemberIds = member_ids.filter(
                (id) => !alreadyEnrolledIds.includes(id)
            );

            if (newMemberIds.length === 0) {
                throw createError(
                    "All selected members are already enrolled",
                    400
                );
            }

            // Create enrollments in batch
            const enrollmentData = newMemberIds.map((member_id) => ({
                class_id: classId,
                member_id,
                level_id: targetLevelId,
                status: "enrolled",
            }));

            const enrollments = await prisma.classEnrollment.createMany({
                data: enrollmentData,
                skipDuplicates: true,
            });

            // Get the created enrollments with member details
            const createdEnrollments = await prisma.classEnrollment.findMany({
                where: {
                    class_id: classId,
                    member_id: { in: newMemberIds },
                },
                include: {
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                        },
                    },
                    level: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            });

            res.status(201).json({
                success: true,
                data: {
                    enrolled_count: enrollments.count,
                    already_enrolled_count: alreadyEnrolledIds.length,
                    enrollments: createdEnrollments,
                    skipped_members: alreadyEnrolledIds,
                },
            });
        } catch (error) {
            next(error);
        }
    }

    // Get enrollment with attendance progress
    async getEnrollmentProgress(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { enrollmentId } = req.params;

            const enrollment = await prisma.classEnrollment.findUnique({
                where: { id: enrollmentId },
                include: {
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                        },
                    },
                    class: {
                        select: {
                            id: true,
                            name: true,
                            has_levels: true,
                        },
                    },
                    level: {
                        select: {
                            id: true,
                            name: true,
                            order_number: true,
                        },
                        include: {
                            sessions: {
                                select: {
                                    id: true,
                                    title: true,
                                    session_date: true,
                                    attendance_meeting: {
                                        select: {
                                            id: true,
                                            participants: {
                                                select: {
                                                    id: true,
                                                    status: true,
                                                    member_id: true,
                                                },
                                            },
                                        },
                                    },
                                },
                                orderBy: { session_date: "asc" },
                            },
                        },
                    },
                },
            });

            if (!enrollment) {
                throw createError("Enrollment not found", 404);
            }

            // Calculate attendance progress
            const sessions = enrollment.level?.sessions || [];
            const totalSessions = sessions.length;
            let attendedSessions = 0;

            sessions.forEach((session: any) => {
                const attendance =
                    session.attendance_meeting?.participants.find(
                        (p: any) =>
                            p.member_id === enrollment.member_id &&
                            p.status === "present"
                    );
                if (attendance) {
                    attendedSessions++;
                }
            });

            const attendanceRate =
                totalSessions > 0 ? attendedSessions / totalSessions : 0;

            const progressData = {
                ...enrollment,
                progress: {
                    total_sessions: totalSessions,
                    attended_sessions: attendedSessions,
                    attendance_rate: Math.round(attendanceRate * 100),
                    can_progress: attendanceRate >= 0.8, // 80% minimum
                    session_details: sessions.map((session: any) => ({
                        id: session.id,
                        title: session.title,
                        session_date: session.session_date,
                        attended: session.attendance_meeting?.participants.some(
                            (p: any) =>
                                p.member_id === enrollment.member_id &&
                                p.status === "present"
                        ),
                    })),
                },
            };

            res.json({ success: true, data: progressData });
        } catch (error) {
            next(error);
        }
    }

    // Promote member to next level
    async promoteToNextLevel(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { enrollmentId } = req.params;
            const { notes } = req.body;

            // Get current enrollment
            const enrollment = await prisma.classEnrollment.findUnique({
                where: { id: enrollmentId },
                include: {
                    level: {
                        include: {
                            class: {
                                include: {
                                    levels: {
                                        orderBy: { order_number: "asc" },
                                    },
                                },
                            },
                        },
                    },
                },
            });

            if (!enrollment) {
                throw createError("Enrollment not found", 404);
            }

            if (!enrollment.level) {
                throw createError(
                    "Cannot promote: enrollment is not associated with a level",
                    400
                );
            }

            // Check if member meets promotion criteria (80% attendance)
            // This would need actual attendance calculation - simplified for now
            const canProgress = true; // TODO: Calculate based on actual attendance

            if (!canProgress) {
                throw createError(
                    "Member does not meet promotion criteria",
                    400
                );
            }

            // Find next level
            const currentOrder = enrollment.level.order_number;
            const nextLevel = enrollment.level.class.levels.find(
                (level) => level.order_number > currentOrder
            );

            if (!nextLevel) {
                // Mark as completed if no next level
                await prisma.classEnrollment.update({
                    where: { id: enrollmentId },
                    data: {
                        status: "completed",
                        completed_at: new Date(),
                    },
                });

                res.json({
                    success: true,
                    data: { message: "Member has completed all levels" },
                });
                return;
            }

            // Update enrollment to next level
            const updatedEnrollment = await prisma.classEnrollment.update({
                where: { id: enrollmentId },
                data: {
                    level_id: nextLevel.id,
                },
                include: {
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                        },
                    },
                    level: {
                        select: {
                            id: true,
                            name: true,
                            order_number: true,
                        },
                    },
                },
            });

            res.json({ success: true, data: updatedEnrollment });
        } catch (error) {
            next(error);
        }
    }
}
