import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';
import { ApiResponse, PaginationQuery } from '../types/api';

export class DistrictController {
  async getDistricts(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { page = 1, limit = 10, search }: PaginationQuery = req.query;
      
      const skip = (page - 1) * limit;
      const take = Math.min(limit, 100);

      const where: any = { status: 'active' };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [districts, total] = await Promise.all([
        prisma.district.findMany({
          where,
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            created_at: true,
            leader1: {
              select: { id: true, first_name: true, last_name: true },
            },
            leader2: {
              select: { id: true, first_name: true, last_name: true },
            },
            _count: {
              select: { 
                cell_groups: { where: { status: 'active' } },
                members: { where: { status: 'active' } },
              },
            },
          },
          orderBy: { name: 'asc' },
          skip,
          take,
        }),
        prisma.district.count({ where }),
      ]);

      res.json({
        success: true,
        data: districts,
        pagination: { page, limit: take, total, totalPages: Math.ceil(total / take) },
      });
    } catch (error) {
      next(error);
    }
  }

  async getDistrictById(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      const district = await prisma.district.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          created_at: true,
          updated_at: true,
          leader1: {
            select: { id: true, first_name: true, last_name: true, email: true, phone: true },
          },
          leader2: {
            select: { id: true, first_name: true, last_name: true, email: true, phone: true },
          },
          cell_groups: {
            select: {
              id: true,
              name: true,
              status: true,
              _count: {
                select: { cell_group_members: { where: { status: 'active' } } },
              },
            },
            where: { status: 'active' },
          },
          _count: {
            select: { 
              cell_groups: { where: { status: 'active' } },
              members: { where: { status: 'active' } },
            },
          },
        },
      });

      if (!district) {
        throw createError('District not found', 404);
      }

      res.json({ success: true, data: district });
    } catch (error) {
      next(error);
    }
  }

  async createDistrict(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const districtData = req.body;

      // Validate leaders exist if provided
      if (districtData.leader1_id) {
        const leader1 = await prisma.member.findUnique({
          where: { id: districtData.leader1_id },
        });
        if (!leader1) {
          throw createError('Leader 1 not found', 400);
        }
      }

      if (districtData.leader2_id) {
        const leader2 = await prisma.member.findUnique({
          where: { id: districtData.leader2_id },
        });
        if (!leader2) {
          throw createError('Leader 2 not found', 400);
        }
      }

      const district = await prisma.district.create({
        data: districtData,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          created_at: true,
        },
      });

      res.status(201).json({ success: true, data: district });
    } catch (error) {
      next(error);
    }
  }

  async updateDistrict(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;
      const updateData = req.body;

      // Check if district exists
      const existingDistrict = await prisma.district.findUnique({
        where: { id },
      });

      if (!existingDistrict) {
        throw createError('District not found', 404);
      }

      const district = await prisma.district.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          updated_at: true,
        },
      });

      res.json({ success: true, data: district });
    } catch (error) {
      next(error);
    }
  }

  async deleteDistrict(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      // Check if district exists
      const existingDistrict = await prisma.district.findUnique({
        where: { id },
      });

      if (!existingDistrict) {
        throw createError('District not found', 404);
      }

      // Soft delete by updating status
      await prisma.district.update({
        where: { id },
        data: { status: 'inactive' },
      });

      res.json({ success: true, data: { message: 'District deleted successfully' } });
    } catch (error) {
      next(error);
    }
  }

  async getDistrictCellGroups(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      const cellGroups = await prisma.cellGroup.findMany({
        where: { 
          district_id: id,
          status: 'active',
        },
        select: {
          id: true,
          name: true,
          description: true,
          meeting_day: true,
          meeting_time: true,
          meeting_location: true,
          status: true,
          leader: {
            select: { id: true, first_name: true, last_name: true },
          },
          assistant_leader: {
            select: { id: true, first_name: true, last_name: true },
          },
          _count: {
            select: { cell_group_members: { where: { status: 'active' } } },
          },
        },
        orderBy: { name: 'asc' },
      });

      res.json({ success: true, data: cellGroups });
    } catch (error) {
      next(error);
    }
  }

  async getDistrictMembers(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;
      const { page = 1, limit = 10 } = req.query;

      const skip = (Number(page) - 1) * Number(limit);
      const take = Math.min(Number(limit), 100);

      const [members, total] = await Promise.all([
        prisma.member.findMany({
          where: { 
            district_id: id,
            status: 'active',
          },
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            phone: true,
            status: true,
            join_date: true,
            cell_group: {
              select: { id: true, name: true },
            },
          },
          orderBy: { last_name: 'asc' },
          skip,
          take,
        }),
        prisma.member.count({
          where: { 
            district_id: id,
            status: 'active',
          },
        }),
      ]);

      res.json({
        success: true,
        data: members,
        pagination: {
          page: Number(page),
          limit: take,
          total,
          totalPages: Math.ceil(total / take),
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
