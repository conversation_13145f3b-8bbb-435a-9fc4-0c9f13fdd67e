import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { createError } from "../middleware/errorHandler";

const prisma = new PrismaClient();

// Get all projects with pagination and filtering
export const getProjects = async (req: Request, res: Response) => {
    try {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        const status = req.query.status as string;
        const is_published = req.query.is_published as string;
        const search = req.query.search as string;

        const skip = (page - 1) * limit;

        // Build where clause
        const where: any = {};

        if (status) {
            where.status = status;
        }

        if (is_published !== undefined) {
            where.is_published = is_published === "true";
        }

        if (search) {
            where.OR = [
                { title: { contains: search } },
                { description: { contains: search } },
            ];
        }

        // Get total count
        const total = await prisma.project.count({ where });

        // Get projects with relations
        const projects = await prisma.project.findMany({
            where,
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                donations: {
                    where: { status: "confirmed" },
                    select: {
                        id: true,
                        donor_name: true,
                        amount: true,
                        is_anonymous: true,
                        donated_at: true,
                    },
                    orderBy: { donated_at: "desc" },
                },
                _count: {
                    select: {
                        donations: {
                            where: { status: "confirmed" },
                        },
                    },
                },
            },
            orderBy: { created_at: "desc" },
            skip,
            take: limit,
        });

        // Calculate progress for each project
        const projectsWithProgress = projects.map((project) => {
            const confirmedDonations = project.donations.filter((d) => d);
            const totalDonated = confirmedDonations.reduce(
                (sum, donation) => sum + Number(donation.amount),
                0
            );
            const progress =
                Number(project.target_amount) > 0
                    ? (totalDonated / Number(project.target_amount)) * 100
                    : 0;

            return {
                ...project,
                current_amount: totalDonated,
                progress: Math.min(progress, 100),
                donations_count: project._count.donations,
            };
        });

        const totalPages = Math.ceil(total / limit);

        res.json({
            success: true,
            data: projectsWithProgress,
            pagination: {
                page,
                limit,
                total,
                totalPages,
            },
        });
    } catch (error) {
        console.error("Error fetching projects:", error);
        throw createError("Failed to fetch projects", 500);
    }
};

// Get single project by ID
export const getProject = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const project = await prisma.project.findUnique({
            where: { id },
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                donations: {
                    where: { status: "confirmed" },
                    select: {
                        id: true,
                        donor_name: true,
                        donor_email: true,
                        amount: true,
                        message: true,
                        is_anonymous: true,
                        donated_at: true,
                    },
                    orderBy: { donated_at: "desc" },
                },
            },
        });

        if (!project) {
            throw createError("Project not found", 404);
        }

        // Calculate current amount and progress
        const totalDonated = project.donations.reduce(
            (sum, donation) => sum + Number(donation.amount),
            0
        );
        const progress =
            Number(project.target_amount) > 0
                ? (totalDonated / Number(project.target_amount)) * 100
                : 0;

        const projectWithProgress = {
            ...project,
            current_amount: totalDonated,
            progress: Math.min(progress, 100),
            donations_count: project.donations.length,
        };

        res.json({
            success: true,
            data: projectWithProgress,
        });
    } catch (error) {
        console.error("Error fetching project:", error);
        throw createError("Failed to fetch project", 500);
    }
};

// Create new project
export const createProject = async (req: Request, res: Response) => {
    try {
        const {
            title,
            description,
            image_url,
            event_date,
            target_amount,
            status = "draft",
            is_published = false,
        } = req.body;

        // Validate required fields
        if (!title || !description || !event_date || !target_amount) {
            throw createError(
                "Missing required fields: title, description, event_date, target_amount",
                400
            );
        }

        // Validate target_amount is positive
        if (Number(target_amount) <= 0) {
            throw createError("Target amount must be greater than 0", 400);
        }

        // Get user ID from auth middleware
        const created_by = (req as any).user.id;

        const project = await prisma.project.create({
            data: {
                title,
                description,
                image_url,
                event_date: new Date(event_date),
                target_amount: Number(target_amount),
                status,
                is_published,
                created_by,
            },
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
        });

        res.status(201).json({
            success: true,
            data: {
                ...project,
                current_amount: 0,
                progress: 0,
                donations_count: 0,
            },
        });
    } catch (error) {
        console.error("Error creating project:", error);
        throw createError("Failed to create project", 500);
    }
};

// Update project
export const updateProject = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const {
            title,
            description,
            image_url,
            event_date,
            target_amount,
            status,
            is_published,
        } = req.body;

        // Check if project exists
        const existingProject = await prisma.project.findUnique({
            where: { id },
        });

        if (!existingProject) {
            throw createError("Project not found", 404);
        }

        // Validate target_amount if provided
        if (target_amount !== undefined && Number(target_amount) <= 0) {
            throw createError("Target amount must be greater than 0", 400);
        }

        // Build update data
        const updateData: any = {};
        if (title !== undefined) updateData.title = title;
        if (description !== undefined) updateData.description = description;
        if (image_url !== undefined) updateData.image_url = image_url;
        if (event_date !== undefined)
            updateData.event_date = new Date(event_date);
        if (target_amount !== undefined)
            updateData.target_amount = Number(target_amount);
        if (status !== undefined) updateData.status = status;
        if (is_published !== undefined) updateData.is_published = is_published;

        const project = await prisma.project.update({
            where: { id },
            data: updateData,
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                donations: {
                    where: { status: "confirmed" },
                    select: {
                        amount: true,
                    },
                },
            },
        });

        // Calculate current amount and progress
        const totalDonated = project.donations.reduce(
            (sum, donation) => sum + Number(donation.amount),
            0
        );
        const progress =
            Number(project.target_amount) > 0
                ? (totalDonated / Number(project.target_amount)) * 100
                : 0;

        res.json({
            success: true,
            data: {
                ...project,
                current_amount: totalDonated,
                progress: Math.min(progress, 100),
                donations_count: project.donations.length,
            },
        });
    } catch (error) {
        console.error("Error updating project:", error);
        throw createError("Failed to update project", 500);
    }
};

// Delete project
export const deleteProject = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        // Check if project exists
        const existingProject = await prisma.project.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        donations: true,
                    },
                },
            },
        });

        if (!existingProject) {
            throw createError("Project not found", 404);
        }

        // Check if project has donations
        if (existingProject._count.donations > 0) {
            throw createError(
                "Cannot delete project with existing donations",
                400
            );
        }

        await prisma.project.delete({
            where: { id },
        });

        res.json({
            success: true,
            message: "Project deleted successfully",
        });
    } catch (error) {
        console.error("Error deleting project:", error);
        throw createError("Failed to delete project", 500);
    }
};

// Get published projects only (for public/member access)
export const getPublishedProjects = async (req: Request, res: Response) => {
    try {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        const search = req.query.search as string;

        const skip = (page - 1) * limit;

        // Build where clause - only published projects
        const where: any = {
            is_published: true,
            status: { not: "cancelled" }, // Exclude cancelled projects
        };

        if (search) {
            where.OR = [
                { title: { contains: search } },
                { description: { contains: search } },
            ];
        }

        // Get total count
        const total = await prisma.project.count({ where });

        // Get projects with relations
        const projects = await prisma.project.findMany({
            where,
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                donations: {
                    where: { status: "confirmed" },
                    select: {
                        id: true,
                        donor_name: true,
                        amount: true,
                        is_anonymous: true,
                        donated_at: true,
                    },
                    orderBy: { donated_at: "desc" },
                },
                _count: {
                    select: {
                        donations: {
                            where: { status: "confirmed" },
                        },
                    },
                },
            },
            orderBy: { created_at: "desc" },
            skip,
            take: limit,
        });

        // Calculate progress for each project
        const projectsWithProgress = projects.map((project) => {
            const confirmedDonations = project.donations.filter((d) => d);
            const totalDonated = confirmedDonations.reduce(
                (sum, donation) => sum + Number(donation.amount),
                0
            );
            const progress =
                Number(project.target_amount) > 0
                    ? (totalDonated / Number(project.target_amount)) * 100
                    : 0;

            return {
                ...project,
                current_amount: totalDonated,
                progress: Math.min(progress, 100),
                donations_count: project._count.donations,
            };
        });

        const totalPages = Math.ceil(total / limit);

        res.json({
            success: true,
            data: projectsWithProgress,
            pagination: {
                page,
                limit,
                total,
                totalPages,
            },
        });
    } catch (error) {
        console.error("Error fetching published projects:", error);
        throw createError("Failed to fetch published projects", 500);
    }
};
