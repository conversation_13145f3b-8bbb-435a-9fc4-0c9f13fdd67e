import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { AuthRequest } from "../middleware/auth";
import {
    ApiResponse,
    PaginationQuery,
    AttendanceMeetingRequest,
} from "../types/api";

export class AttendanceController {
    async getMeetings(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const {
                page = 1,
                limit = 10,
                search,
                event_category,
                date_from,
                date_to,
                start_date,
                end_date,
                cell_group_id,
                ministry_id,
            }: PaginationQuery & {
                event_category?: string;
                date_from?: string;
                date_to?: string;
                start_date?: string;
                end_date?: string;
                cell_group_id?: string;
                ministry_id?: string;
            } = req.query;

            const skip = (page - 1) * limit;
            const take = Math.min(limit, 1000); // Increase max limit to 1000

            const where: any = {};

            if (search) {
                where.OR = [
                    { topic: { contains: search } },
                    { meeting_type: { contains: search } },
                ];
            }

            if (event_category) {
                where.event_category = event_category;
            }

            const startDateValue = start_date || date_from;
            const endDateValue = end_date || date_to;

            if (startDateValue) {
                where.meeting_date = {
                    ...where.meeting_date,
                    gte: new Date(startDateValue),
                };
            }

            if (endDateValue) {
                where.meeting_date = {
                    ...where.meeting_date,
                    lte: new Date(endDateValue),
                };
            }

            if (cell_group_id) {
                where.cell_group_id = cell_group_id;
            }

            if (ministry_id) {
                where.ministry_id = ministry_id;
            }

            const [meetings, total] = await Promise.all([
                prisma.attendanceMeeting.findMany({
                    where,
                    select: {
                        id: true,
                        event_category: true,
                        meeting_date: true,
                        meeting_type: true,
                        topic: true,
                        location: true,
                        offering: true,
                        is_realtime: true,
                        created_at: true,
                        cell_group: {
                            select: { id: true, name: true },
                        },
                        ministry: {
                            select: { id: true, name: true },
                        },
                        class_sessions: {
                            select: {
                                id: true,
                                title: true,
                                class: {
                                    select: { id: true, name: true },
                                },
                                level: {
                                    select: { id: true, name: true },
                                },
                            },
                        },
                        _count: {
                            select: {
                                participants: {
                                    where: {
                                        status: {
                                            in: ["present", "late"],
                                        },
                                    },
                                },
                                visitors: true,
                            },
                        },
                    },
                    orderBy: { meeting_date: "desc" },
                    skip,
                    take,
                }),
                prisma.attendanceMeeting.count({ where }),
            ]);

            res.json({
                success: true,
                data: meetings,
                pagination: {
                    page,
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        } catch (error) {
            next(error);
        }
    }

    async getMeetingById(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;

            const meeting = await prisma.attendanceMeeting.findUnique({
                where: { id },
                select: {
                    id: true,
                    event_category: true,
                    meeting_date: true,
                    meeting_type: true,
                    topic: true,
                    notes: true,
                    location: true,
                    offering: true,
                    is_realtime: true,
                    created_at: true,
                    updated_at: true,
                    cell_group: {
                        select: { id: true, name: true },
                    },
                    ministry: {
                        select: { id: true, name: true },
                    },
                    participants: {
                        select: {
                            id: true,
                            status: true,
                            notes: true,
                            member: {
                                select: {
                                    id: true,
                                    first_name: true,
                                    last_name: true,
                                    email: true,
                                },
                            },
                        },
                    },
                    visitors: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            phone: true,
                            email: true,
                            notes: true,
                        },
                    },
                },
            });

            if (!meeting) {
                throw createError("Meeting not found", 404);
            }

            res.json({ success: true, data: meeting });
        } catch (error) {
            next(error);
        }
    }

    async createMeeting(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            console.log(
                "Creating meeting with data:",
                JSON.stringify(req.body, null, 2)
            );

            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                console.log("Validation errors:", errors.array());
                throw createError("Validation failed", 400);
            }

            const {
                participants,
                visitors,
                class_id,
                level_id,
                session_id,
                ...meetingData
            }: AttendanceMeetingRequest & {
                class_id?: string;
                level_id?: string;
                session_id?: string;
            } = req.body;

            // Create meeting with participants and visitors in a transaction
            const result = await prisma.$transaction(async (tx) => {
                // Create the meeting
                const meeting = await tx.attendanceMeeting.create({
                    data: {
                        ...meetingData,
                        meeting_date: new Date(meetingData.meeting_date),
                    },
                });

                console.log("Created meeting:", meeting.id);

                // If this is a class meeting with session_id, link it to class_sessions
                if (meetingData.event_category === "class" && session_id) {
                    await tx.classSession.updateMany({
                        where: {
                            id: session_id,
                            OR: [
                                { class_id: class_id },
                                { level_id: level_id },
                            ],
                        },
                        data: {
                            attendance_meeting_id: meeting.id,
                        },
                    });
                    console.log(
                        `Linked session ${session_id} to meeting ${meeting.id}`
                    );
                }

                // Create participants
                if (participants && participants.length > 0) {
                    await tx.attendanceParticipant.createMany({
                        data: participants.map((p) => ({
                            meeting_id: meeting.id,
                            member_id: p.member_id,
                            status: p.status,
                            notes: p.notes,
                        })),
                    });
                }

                // Create visitors
                if (visitors && visitors.length > 0) {
                    await tx.attendanceVisitor.createMany({
                        data: visitors.map((v) => ({
                            meeting_id: meeting.id,
                            first_name:
                                v.first_name || v.name?.split(" ")[0] || "",
                            last_name:
                                v.last_name ||
                                v.name?.split(" ").slice(1).join(" ") ||
                                "",
                            phone: v.phone,
                            email: v.email,
                            notes: v.notes,
                        })),
                    });
                }

                return meeting;
            });

            // Emit real-time update if enabled
            if (result.is_realtime) {
                const io = req.app.get("io");
                io.to(`meeting-${result.id}`).emit("attendance-updated", {
                    meetingId: result.id,
                    participantCount: participants?.length || 0,
                    visitorCount: visitors?.length || 0,
                });
            }

            res.status(201).json({ success: true, data: result });
        } catch (error) {
            next(error);
        }
    }

    async updateMeeting(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const { participants, visitors, ...updateData } = req.body;

            // Check if meeting exists
            const existingMeeting = await prisma.attendanceMeeting.findUnique({
                where: { id },
            });

            if (!existingMeeting) {
                throw createError("Meeting not found", 404);
            }

            // Use transaction for updating meeting, participants, and visitors
            const result = await prisma.$transaction(async (tx) => {
                // Update meeting
                const meeting = await tx.attendanceMeeting.update({
                    where: { id },
                    data: {
                        ...updateData,
                        meeting_date: updateData.meeting_date
                            ? new Date(updateData.meeting_date)
                            : undefined,
                    },
                });

                // Update participants if provided
                if (participants && Array.isArray(participants)) {
                    // Delete existing participants
                    await tx.attendanceParticipant.deleteMany({
                        where: { meeting_id: id },
                    });

                    // Create new participants
                    if (participants.length > 0) {
                        await tx.attendanceParticipant.createMany({
                            data: participants.map((p: any) => ({
                                meeting_id: id,
                                member_id: p.member_id,
                                status: p.status,
                                notes: p.notes,
                            })),
                        });
                    }
                }

                // Update visitors if provided
                if (visitors && Array.isArray(visitors)) {
                    // Delete existing visitors
                    await tx.attendanceVisitor.deleteMany({
                        where: { meeting_id: id },
                    });

                    // Create new visitors
                    if (visitors.length > 0) {
                        await tx.attendanceVisitor.createMany({
                            data: visitors.map((v: any) => ({
                                meeting_id: id,
                                first_name:
                                    v.first_name || v.name?.split(" ")[0] || "",
                                last_name:
                                    v.last_name ||
                                    v.name?.split(" ").slice(1).join(" ") ||
                                    "",
                                phone: v.phone,
                                email: v.email,
                                notes: v.notes,
                            })),
                        });
                    }
                }

                return meeting;
            });

            res.json({ success: true, data: result });
        } catch (error) {
            next(error);
        }
    }

    async deleteMeeting(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;

            // Check if meeting exists
            const existingMeeting = await prisma.attendanceMeeting.findUnique({
                where: { id },
            });

            if (!existingMeeting) {
                throw createError("Meeting not found", 404);
            }

            // Delete meeting and related records (cascade)
            await prisma.attendanceMeeting.delete({
                where: { id },
            });

            res.json({
                success: true,
                data: { message: "Meeting deleted successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async getMeetingParticipants(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;

            const participants = await prisma.attendanceParticipant.findMany({
                where: { meeting_id: id },
                select: {
                    id: true,
                    status: true,
                    notes: true,
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                        },
                    },
                },
                orderBy: {
                    member: { last_name: "asc" },
                },
            });

            res.json({ success: true, data: participants });
        } catch (error) {
            next(error);
        }
    }

    async updateMeetingParticipants(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const { participants } = req.body;

            // Update participants in a transaction
            await prisma.$transaction(async (tx) => {
                // Delete existing participants
                await tx.attendanceParticipant.deleteMany({
                    where: { meeting_id: id },
                });

                // Create new participants
                if (participants && participants.length > 0) {
                    await tx.attendanceParticipant.createMany({
                        data: participants.map((p: any) => ({
                            meeting_id: id,
                            member_id: p.member_id,
                            status: p.status,
                            notes: p.notes,
                        })),
                    });
                }
            });

            // Emit real-time update
            const meeting = await prisma.attendanceMeeting.findUnique({
                where: { id },
                select: { is_realtime: true },
            });

            if (meeting?.is_realtime) {
                const io = req.app.get("io");
                io.to(`meeting-${id}`).emit("attendance-updated", {
                    meetingId: id,
                    participantCount: participants?.length || 0,
                });
            }

            res.json({
                success: true,
                data: { message: "Participants updated successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async getMeetingVisitors(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;

            const visitors = await prisma.attendanceVisitor.findMany({
                where: { meeting_id: id },
                select: {
                    id: true,
                    first_name: true,
                    last_name: true,
                    phone: true,
                    email: true,
                    notes: true,
                    created_at: true,
                },
                orderBy: { first_name: "asc" },
            });

            res.json({ success: true, data: visitors });
        } catch (error) {
            next(error);
        }
    }

    async addMeetingVisitors(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const { visitors } = req.body;

            // Add visitors
            await prisma.attendanceVisitor.createMany({
                data: visitors.map((v: any) => ({
                    meeting_id: id,
                    first_name: v.first_name || v.name?.split(" ")[0] || "",
                    last_name:
                        v.last_name ||
                        v.name?.split(" ").slice(1).join(" ") ||
                        "",
                    phone: v.phone,
                    email: v.email,
                    notes: v.notes,
                })),
            });

            res.json({
                success: true,
                data: { message: "Visitors added successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async getAttendanceStats(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { start_date, end_date, event_category } = req.query;

            const where: any = {};

            if (start_date) {
                where.meeting_date = {
                    ...where.meeting_date,
                    gte: new Date(start_date as string),
                };
            }

            if (end_date) {
                where.meeting_date = {
                    ...where.meeting_date,
                    lte: new Date(end_date as string),
                };
            }

            if (event_category) {
                where.event_category = event_category;
            }

            const [
                totalMeetings,
                totalParticipants,
                totalVisitors,
                presentCount,
                absentCount,
                lateCount,
            ] = await Promise.all([
                prisma.attendanceMeeting.count({ where }),
                prisma.attendanceParticipant.count({
                    where: { meeting: where },
                }),
                prisma.attendanceVisitor.count({
                    where: { meeting: where },
                }),
                prisma.attendanceParticipant.count({
                    where: {
                        meeting: where,
                        status: "present",
                    },
                }),
                prisma.attendanceParticipant.count({
                    where: {
                        meeting: where,
                        status: "absent",
                    },
                }),
                prisma.attendanceParticipant.count({
                    where: {
                        meeting: where,
                        status: "late",
                    },
                }),
            ]);

            const stats = {
                totalMeetings,
                totalParticipants,
                totalVisitors,
                presentCount,
                absentCount,
                lateCount,
                attendanceRate:
                    totalParticipants > 0
                        ? (
                              ((presentCount + lateCount) / totalParticipants) *
                              100
                          ).toFixed(2)
                        : "0.00",
            };

            res.json({ success: true, data: stats });
        } catch (error) {
            next(error);
        }
    }

    async getMemberAttendanceHistory(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { memberId } = req.params;
            const { page = 1, limit = 10 } = req.query;

            const skip = (Number(page) - 1) * Number(limit);
            const take = Math.min(Number(limit), 100);

            const [attendance, total] = await Promise.all([
                prisma.attendanceParticipant.findMany({
                    where: { member_id: memberId },
                    select: {
                        id: true,
                        status: true,
                        notes: true,
                        meeting: {
                            select: {
                                id: true,
                                meeting_date: true,
                                meeting_type: true,
                                topic: true,
                                event_category: true,
                                cell_group: {
                                    select: { name: true },
                                },
                                ministry: {
                                    select: { name: true },
                                },
                            },
                        },
                    },
                    orderBy: {
                        meeting: { meeting_date: "desc" },
                    },
                    skip,
                    take,
                }),
                prisma.attendanceParticipant.count({
                    where: { member_id: memberId },
                }),
            ]);

            res.json({
                success: true,
                data: attendance,
                pagination: {
                    page: Number(page),
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        } catch (error) {
            next(error);
        }
    }

    async toggleMeetingRealtime(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const { is_realtime } = req.body;

            const meeting = await prisma.attendanceMeeting.update({
                where: { id },
                data: { is_realtime },
                select: {
                    id: true,
                    is_realtime: true,
                    topic: true,
                },
            });

            res.json({ success: true, data: meeting });
        } catch (error) {
            next(error);
        }
    }

    async getMemberAttendance(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { memberId } = req.params;
            const {
                page = 1,
                limit = 10,
                timeFilter = "month",
            }: PaginationQuery & {
                timeFilter?: "all" | "month" | "quarter" | "year";
            } = req.query;

            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);

            // Calculate date range based on filter
            const today = new Date();
            let startDate = new Date();

            if (timeFilter === "month") {
                startDate.setMonth(today.getMonth() - 1);
            } else if (timeFilter === "quarter") {
                startDate.setMonth(today.getMonth() - 3);
            } else if (timeFilter === "year") {
                startDate.setFullYear(today.getFullYear() - 1);
            } else {
                // All time - set to a far past date
                startDate = new Date(2000, 0, 1);
            }

            const where: any = {
                member_id: memberId,
                meeting: {
                    meeting_date: {
                        gte: startDate,
                        lte: today,
                    },
                },
            };

            const [attendanceRecords, total] = await Promise.all([
                prisma.attendanceParticipant.findMany({
                    where,
                    skip,
                    take,
                    orderBy: {
                        meeting: {
                            meeting_date: "desc",
                        },
                    },
                    include: {
                        meeting: {
                            select: {
                                id: true,
                                meeting_date: true,
                                meeting_type: true,
                                topic: true,
                                event_category: true,
                                location: true,
                                cell_group: {
                                    select: { id: true, name: true },
                                },
                                ministry: {
                                    select: { id: true, name: true },
                                },
                            },
                        },
                    },
                }),
                prisma.attendanceParticipant.count({ where }),
            ]);

            // Calculate statistics
            const allRecords = await prisma.attendanceParticipant.findMany({
                where: {
                    member_id: memberId,
                    meeting: {
                        meeting_date: {
                            gte: startDate,
                            lte: today,
                        },
                    },
                },
                select: { status: true },
            });

            const stats = {
                total: allRecords.length,
                present: allRecords.filter((r: any) => r.status === "present")
                    .length,
                absent: allRecords.filter((r: any) => r.status === "absent")
                    .length,
                late: allRecords.filter((r: any) => r.status === "late").length,
                percentage: 0,
            };

            stats.percentage =
                stats.total > 0
                    ? Math.round((stats.present / stats.total) * 100)
                    : 0;

            const response: ApiResponse<any> = {
                success: true,
                data: {
                    records: attendanceRecords,
                    stats,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit),
                    },
                },
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }

    async convertVisitorToMember(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params; // visitor ID
            const {
                cell_group_id,
                district_id,
                baptism_date,
                join_date,
                additional_info,
            } = req.body;

            // Find the visitor
            const visitor = await prisma.attendanceVisitor.findUnique({
                where: { id },
                include: {
                    meeting: {
                        select: {
                            cell_group: {
                                select: {
                                    id: true,
                                    name: true,
                                    district_id: true,
                                },
                            },
                        },
                    },
                },
            });

            if (!visitor) {
                throw createError("Visitor not found", 404);
            }

            if (visitor.converted_to_member_id) {
                throw createError("Visitor already converted to member", 400);
            }

            // Use transaction to create member and update visitor
            const result = await prisma.$transaction(async (tx) => {
                // Create new member from visitor data
                const newMember = await tx.member.create({
                    data: {
                        first_name: visitor.first_name,
                        last_name: visitor.last_name || "",
                        email:
                            visitor.email ||
                            `${visitor.first_name.toLowerCase()}.${
                                visitor.last_name?.toLowerCase() || "visitor"
                            }@temp.com`,
                        phone: visitor.phone || null,
                        cell_group_id:
                            cell_group_id ||
                            visitor.meeting.cell_group?.id ||
                            null,
                        district_id:
                            district_id ||
                            visitor.meeting.cell_group?.district_id ||
                            null,
                        join_date: join_date ? new Date(join_date) : new Date(),
                        notes:
                            additional_info ||
                            `Converted from visitor. Original notes: ${
                                visitor.notes || ""
                            }`,
                        status: "active",
                        role: "member",
                    },
                });

                // Update visitor with member reference
                await tx.attendanceVisitor.update({
                    where: { id },
                    data: {
                        converted_to_member_id: newMember.id,
                    },
                });

                return newMember;
            });

            res.json({
                success: true,
                data: {
                    message: "Visitor successfully converted to member",
                    member: result,
                },
            });
        } catch (error) {
            next(error);
        }
    }

    async getConvertedVisitors(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { page = 1, limit = 10 } = req.query;
            const skip = (Number(page) - 1) * Number(limit);
            const take = Math.min(Number(limit), 100);

            const [convertedVisitors, total] = await Promise.all([
                prisma.attendanceVisitor.findMany({
                    where: {
                        converted_to_member_id: {
                            not: null,
                        },
                    },
                    include: {
                        converted_to_member: {
                            select: {
                                id: true,
                                first_name: true,
                                last_name: true,
                                email: true,
                                phone: true,
                                cell_group: {
                                    select: { name: true },
                                },
                            },
                        },
                        meeting: {
                            select: {
                                meeting_date: true,
                                topic: true,
                                cell_group: {
                                    select: { name: true },
                                },
                            },
                        },
                    },
                    orderBy: { created_at: "desc" },
                    skip,
                    take,
                }),
                prisma.attendanceVisitor.count({
                    where: {
                        converted_to_member_id: {
                            not: null,
                        },
                    },
                }),
            ]);

            res.json({
                success: true,
                data: convertedVisitors,
                pagination: {
                    page: Number(page),
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        } catch (error) {
            next(error);
        }
    }

    // Live Attendance Methods
    async toggleLiveAttendance(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;
            const { active, expires_at } = req.body;

            const meeting = await prisma.attendanceMeeting.findUnique({
                where: { id },
            });

            if (!meeting) {
                throw createError("Meeting not found", 404);
            }

            // Generate QR code data if activating live attendance
            let qrCodeData = null;
            if (active) {
                // Option 1: Full URL (for production/HTTPS)
                if (
                    process.env.FRONTEND_URL &&
                    !process.env.FRONTEND_URL.includes("localhost")
                ) {
                    qrCodeData = `${process.env.FRONTEND_URL}/attendance/${id}/live-checkin`;
                } else {
                    // Option 2: Meeting ID only (for development/localhost)
                    // Frontend will construct the URL based on current location
                    qrCodeData = `MEETING_ID:${id}`;
                }
            }

            const updatedMeeting = await prisma.attendanceMeeting.update({
                where: { id },
                data: {
                    live_checkin_active: active,
                    live_checkin_expires_at: expires_at
                        ? new Date(expires_at)
                        : null,
                    qr_code_data: qrCodeData,
                },
            });

            res.json({
                success: true,
                data: updatedMeeting,
                message: `Live attendance ${
                    active ? "activated" : "deactivated"
                }`,
            });
        } catch (error) {
            next(error);
        }
    }

    async getLiveAttendanceStatus(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;

            const meeting = await prisma.attendanceMeeting.findUnique({
                where: { id },
                select: {
                    id: true,
                    topic: true,
                    meeting_date: true,
                    live_checkin_active: true,
                    live_checkin_expires_at: true,
                    qr_code_data: true,
                },
            });

            if (!meeting) {
                throw createError("Meeting not found", 404);
            }

            // Check if live attendance has expired
            const now = new Date();
            const isExpired =
                meeting.live_checkin_expires_at &&
                meeting.live_checkin_expires_at < now;

            res.json({
                success: true,
                data: {
                    ...meeting,
                    is_active: meeting.live_checkin_active && !isExpired,
                    is_expired: isExpired,
                },
            });
        } catch (error) {
            next(error);
        }
    }

    async liveCheckin(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params; // meeting_id
            const { member_id } = req.body;

            // Verify meeting exists and live attendance is active
            const meeting = await prisma.attendanceMeeting.findUnique({
                where: { id },
                select: {
                    id: true,
                    topic: true,
                    live_checkin_active: true,
                    live_checkin_expires_at: true,
                },
            });

            if (!meeting) {
                throw createError("Meeting not found", 404);
            }

            if (!meeting.live_checkin_active) {
                throw createError(
                    "Live attendance is not active for this meeting",
                    400
                );
            }

            // Check if live attendance has expired
            const now = new Date();
            if (
                meeting.live_checkin_expires_at &&
                meeting.live_checkin_expires_at < now
            ) {
                throw createError("Live attendance has expired", 400);
            }

            // Check if member is already checked in
            const existingParticipant =
                await prisma.attendanceParticipant.findUnique({
                    where: {
                        meeting_id_member_id: {
                            meeting_id: id,
                            member_id: member_id,
                        },
                    },
                });

            if (!existingParticipant) {
                throw createError(
                    "Member is not enrolled in this meeting",
                    400
                );
            }

            if (existingParticipant.status === "present") {
                return res.json({
                    success: true,
                    message: "You have already checked in for this meeting",
                    data: existingParticipant,
                    already_checked_in: true,
                });
            }

            // Update participant status to present
            const updatedParticipant =
                await prisma.attendanceParticipant.update({
                    where: {
                        meeting_id_member_id: {
                            meeting_id: id,
                            member_id: member_id,
                        },
                    },
                    data: {
                        status: "present",
                        updated_at: now,
                    },
                    include: {
                        member: {
                            select: {
                                id: true,
                                first_name: true,
                                last_name: true,
                            },
                        },
                    },
                });

            // Emit real-time update if Socket.IO is available
            if (req.app.get("io")) {
                req.app.get("io").emit("attendance_update", {
                    meeting_id: id,
                    participant: updatedParticipant,
                    action: "live_checkin",
                });
            }

            res.json({
                success: true,
                message: "Successfully checked in!",
                data: updatedParticipant,
                already_checked_in: false,
            });
        } catch (error) {
            next(error);
        }
    }
}
