import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { AuthRequest } from "../middleware/auth";
import { comparePassword, hashPassword } from "../utils/password";
import {
    generateAccessToken,
    generateRefreshToken,
    verifyRefreshToken,
} from "../utils/jwt";
import { ApiResponse, LoginRequest, LoginResponse } from "../types/api";

export class AuthController {
    async adminLogin(req: Request, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { email, password }: LoginRequest = req.body;

            console.log("🔍 Admin login attempt for:", email);

            // Find user by email
            const user = await prisma.member.findUnique({
                where: { email },
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    password_hash: true,
                    role: true,
                    role_level: true,
                    status: true,
                },
            });

            console.log("👤 User found:", user ? "Yes" : "No");
            if (user) {
                console.log("📧 Email:", user.email);
                console.log("👤 Role:", user.role);
                console.log("🔢 Role Level:", user.role_level);
                console.log("📊 Status:", user.status);
                console.log("🔐 Has password hash:", !!user.password_hash);
            }

            if (!user || user.status !== "active") {
                console.log("❌ User not found or inactive");
                throw createError("Invalid credentials", 401);
            }

            // Check if user is admin
            const isAdmin = user.role === "admin" || user.role_level >= 4;
            console.log(
                "🔐 Is admin?",
                isAdmin,
                `(role: ${user.role}, level: ${user.role_level})`
            );

            if (!isAdmin) {
                console.log("❌ Admin access required");
                throw createError("Admin access required", 403);
            }

            // Verify password
            console.log("🔑 Verifying password...");
            const passwordValid =
                user.password_hash &&
                (await comparePassword(password, user.password_hash));
            console.log("✅ Password valid:", passwordValid);

            if (!passwordValid) {
                console.log("❌ Invalid password");
                throw createError("Invalid credentials", 401);
            }

            // Generate tokens
            const tokenPayload = {
                id: user.id,
                email: user.email,
                role: user.role,
                role_level: user.role_level,
            };

            const accessToken = generateAccessToken(tokenPayload);
            const refreshToken = generateRefreshToken(tokenPayload);

            const response: ApiResponse<LoginResponse> = {
                success: true,
                data: {
                    user: {
                        id: user.id,
                        email: user.email,
                        first_name: user.first_name,
                        last_name: user.last_name,
                        role: user.role,
                        role_level: user.role_level,
                    },
                    tokens: {
                        access_token: accessToken,
                        refresh_token: refreshToken,
                    },
                },
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }

    async memberLogin(req: Request, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { email, password }: LoginRequest = req.body;

            // Find user by email
            const user = await prisma.member.findUnique({
                where: { email },
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    password_hash: true,
                    password_reset_required: true,
                    role: true,
                    role_level: true,
                    status: true,
                },
            });

            if (!user || user.status !== "active") {
                throw createError("Invalid credentials", 401);
            }

            // Check if password is set
            if (!user.password_hash) {
                const response: ApiResponse = {
                    success: false,
                    error: {
                        message: "Password not set for this account",
                        code: "PASSWORD_NOT_SET",
                    },
                };
                return res.status(401).json(response);
            }

            // Verify password
            if (!(await comparePassword(password, user.password_hash))) {
                throw createError("Invalid credentials", 401);
            }

            // Generate tokens
            const tokenPayload = {
                id: user.id,
                email: user.email,
                role: user.role,
                role_level: user.role_level,
            };

            const accessToken = generateAccessToken(tokenPayload);
            const refreshToken = generateRefreshToken(tokenPayload);

            const response: ApiResponse<
                LoginResponse & { passwordResetRequired?: boolean }
            > = {
                success: true,
                data: {
                    user: {
                        id: user.id,
                        email: user.email,
                        first_name: user.first_name,
                        last_name: user.last_name,
                        role: user.role,
                        role_level: user.role_level,
                    },
                    tokens: {
                        access_token: accessToken,
                        refresh_token: refreshToken,
                    },
                    passwordResetRequired: user.password_reset_required,
                },
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }

    async checkMember(req: Request, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { email } = req.body;

            const member = await prisma.member.findUnique({
                where: { email },
                select: {
                    id: true,
                    email: true,
                    password_hash: true,
                },
            });

            if (!member) {
                throw createError("Member not found", 404);
            }

            const response: ApiResponse = {
                success: true,
                data: {
                    memberId: member.id,
                    hasPassword: !!member.password_hash,
                },
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }

    async resetPassword(req: Request, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { memberId, currentPassword, newPassword } = req.body;

            const member = await prisma.member.findUnique({
                where: { id: memberId },
                select: {
                    id: true,
                    password_hash: true,
                },
            });

            if (!member) {
                throw createError("Member not found", 404);
            }

            // Verify current password
            if (
                !member.password_hash ||
                !(await comparePassword(currentPassword, member.password_hash))
            ) {
                throw createError("Current password is incorrect", 400);
            }

            // Hash new password
            const hashedPassword = await hashPassword(newPassword);

            // Update password
            await prisma.member.update({
                where: { id: memberId },
                data: {
                    password_hash: hashedPassword,
                    password_reset_required: false,
                },
            });

            const response: ApiResponse = {
                success: true,
                data: { message: "Password updated successfully" },
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }

    async refreshToken(req: Request, res: Response, next: NextFunction) {
        try {
            const { refresh_token } = req.body;

            if (!refresh_token) {
                throw createError("Refresh token required", 401);
            }

            const decoded = verifyRefreshToken(refresh_token);

            // Verify user still exists and is active
            const user = await prisma.member.findUnique({
                where: { id: decoded.id },
                select: {
                    id: true,
                    email: true,
                    role: true,
                    role_level: true,
                    status: true,
                },
            });

            if (!user || user.status !== "active") {
                throw createError("User not found or inactive", 401);
            }

            // Generate new tokens
            const tokenPayload = {
                id: user.id,
                email: user.email,
                role: user.role,
                role_level: user.role_level,
            };

            const accessToken = generateAccessToken(tokenPayload);
            const newRefreshToken = generateRefreshToken(tokenPayload);

            const response: ApiResponse = {
                success: true,
                data: {
                    access_token: accessToken,
                    refresh_token: newRefreshToken,
                },
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }

    async logout(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            // In a more sophisticated implementation, you might want to blacklist the token
            // For now, we'll just return success
            const response: ApiResponse = {
                success: true,
                data: { message: "Logged out successfully" },
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }

    async getCurrentUser(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            if (!req.user) {
                throw createError("User not found", 404);
            }

            const user = await prisma.member.findUnique({
                where: { id: req.user.id },
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    phone: true,
                    address: true,
                    date_of_birth: true,
                    gender: true,
                    marital_status: true,
                    join_date: true,
                    role: true,
                    role_level: true,
                    cell_group_id: true,
                    district_id: true,
                    cell_group: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    district: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            });

            if (!user) {
                throw createError("User not found", 404);
            }

            const response: ApiResponse = {
                success: true,
                data: user,
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }

    async changePassword(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { currentPassword, newPassword } = req.body;
            const userId = req.user?.id;

            if (!userId) {
                throw createError("User not authenticated", 401);
            }

            const member = await prisma.member.findUnique({
                where: { id: userId },
                select: {
                    id: true,
                    password_hash: true,
                },
            });

            if (!member) {
                throw createError("Member not found", 404);
            }

            if (!member.password_hash) {
                throw createError("No password set for this member", 400);
            }

            // Verify current password
            const isCurrentPasswordValid = await comparePassword(
                currentPassword,
                member.password_hash
            );
            if (!isCurrentPasswordValid) {
                throw createError("Current password is incorrect", 400);
            }

            // Hash new password
            const hashedNewPassword = await hashPassword(newPassword);

            // Update password
            await prisma.member.update({
                where: { id: userId },
                data: {
                    password_hash: hashedNewPassword,
                    password_reset_required: false,
                },
            });

            const response: ApiResponse = {
                success: true,
                data: { message: "Password changed successfully" },
            };

            res.json(response);
        } catch (error) {
            next(error);
        }
    }
}
