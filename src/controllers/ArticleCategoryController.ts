import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { AuthRequest } from "../middleware/auth";

export class ArticleCategoryController {
    async getCategories(req: Request, res: Response, next: NextFunction) {
        try {
            // Get categories from ArticleCategory table
            const dbCategories = await prisma.articleCategory.findMany({
                select: {
                    id: true,
                    name: true,
                    description: true,
                    icon: true,
                },
                orderBy: { name: "asc" },
            });

            // Also get unique categories from existing articles
            const articleCategories = await prisma.article.findMany({
                select: { category: true },
                distinct: ["category"],
                where: {
                    category: { not: "" },
                },
            });

            // Combine both sources and remove duplicates
            const allCategories = new Set();

            // Add formal categories
            dbCategories.forEach((cat) => allCategories.add(cat.name));

            // Add categories from articles
            articleCategories.forEach((article) => {
                if (article.category) {
                    allCategories.add(article.category);
                }
            });

            // Convert to array and create response
            const categories = Array.from(allCategories).map((name) => {
                const dbCategory = dbCategories.find(
                    (cat) => cat.name === name
                );
                return dbCategory || { name, description: null, icon: null };
            });

            res.json({ success: true, data: categories });
        } catch (error) {
            next(error);
        }
    }

    async createCategory(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { name, description, icon } = req.body;

            // Check if category already exists
            const existingCategory = await prisma.articleCategory.findFirst({
                where: { name },
            });

            if (existingCategory) {
                throw createError("Category already exists", 409);
            }

            const category = await prisma.articleCategory.create({
                data: { name, description, icon },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    icon: true,
                },
            });

            res.status(201).json({ success: true, data: category });
        } catch (error) {
            next(error);
        }
    }

    async updateCategory(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const { name, description, icon } = req.body;

            const category = await prisma.articleCategory.update({
                where: { id },
                data: { name, description, icon },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    icon: true,
                },
            });

            res.json({ success: true, data: category });
        } catch (error: any) {
            if (error.code === "P2025") {
                next(createError("Category not found", 404));
            } else {
                next(error);
            }
        }
    }

    async deleteCategory(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;

            // Check if category is being used by any articles
            const articleCount = await prisma.article.count({
                where: {
                    category: {
                        in: await prisma.articleCategory
                            .findUnique({
                                where: { id },
                                select: { name: true },
                            })
                            .then((cat) => (cat ? [cat.name] : [])),
                    },
                },
            });

            if (articleCount > 0) {
                throw createError(
                    "Cannot delete category that is being used by articles",
                    400
                );
            }

            await prisma.articleCategory.delete({ where: { id } });

            res.json({
                success: true,
                message: "Category deleted successfully",
            });
        } catch (error: any) {
            if (error.code === "P2025") {
                next(createError("Category not found", 404));
            } else {
                next(error);
            }
        }
    }
}
