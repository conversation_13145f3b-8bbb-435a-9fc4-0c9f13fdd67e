import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { AuthRequest } from "../middleware/auth";
import { hashPassword, generateDefaultPassword } from "../utils/password";
import {
    ApiResponse,
    PaginationQuery,
    MemberCreateRequest,
} from "../types/api";

export class MemberController {
    async getMembers(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const {
                page = 1,
                limit = 10,
                search,
                district_id,
                cell_group_id,
                no_cell_group,
            }: PaginationQuery & {
                district_id?: string;
                cell_group_id?: string;
                no_cell_group?: string;
            } = req.query;

            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);

            const where: any = { status: "active" };

            if (search) {
                // For MySQL, we use contains without mode (case-insensitive by default for varchar fields)
                where.OR = [
                    { first_name: { contains: search } },
                    { last_name: { contains: search } },
                    { email: { contains: search } },
                ];
            }

            if (district_id) {
                where.district_id = district_id;
            }

            if (cell_group_id) {
                where.cell_group_id = cell_group_id;
            }

            // Filter untuk member yang tidak memiliki cell group
            if (no_cell_group === "true") {
                where.NOT = {
                    cell_group_memberships: {
                        some: {
                            status: "active",
                        },
                    },
                };
            }

            const [members, total] = await Promise.all([
                prisma.member.findMany({
                    where,
                    select: {
                        id: true,
                        email: true,
                        first_name: true,
                        last_name: true,
                        phone: true,
                        status: true,
                        role: true,
                        join_date: true,
                        cell_group: { select: { id: true, name: true } },
                        district: { select: { id: true, name: true } },
                    },
                    orderBy: { last_name: "asc" },
                    skip,
                    take,
                }),
                prisma.member.count({ where }),
            ]);

            res.json({
                success: true,
                data: members,
                pagination: {
                    page,
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        } catch (error) {
            next(error);
        }
    }

    async getMemberById(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;
            const member = await prisma.member.findUnique({
                where: { id },
                include: {
                    cell_group: {
                        select: {
                            id: true,
                            name: true,
                            district: {
                                select: { id: true, name: true },
                            },
                        },
                    },
                    district: { select: { id: true, name: true } },
                    cell_group_memberships: {
                        include: {
                            cell_group: {
                                select: {
                                    id: true,
                                    name: true,
                                    district: {
                                        select: { id: true, name: true },
                                    },
                                },
                            },
                        },
                    },
                    cell_group_leaderships: {
                        include: {
                            cell_group: {
                                select: {
                                    id: true,
                                    name: true,
                                    district: {
                                        select: { id: true, name: true },
                                    },
                                },
                            },
                        },
                    },
                    led_districts_1: { select: { id: true, name: true } },
                    led_districts_2: { select: { id: true, name: true } },
                },
            });

            if (!member) throw createError("Member not found", 404);

            // Remove sensitive fields
            const { password_hash, ...memberData } = member;

            res.json({ success: true, data: memberData });
        } catch (error) {
            next(error);
        }
    }

    async createMember(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const memberData: MemberCreateRequest = req.body;

            const existingMember = await prisma.member.findUnique({
                where: { email: memberData.email },
            });
            if (existingMember) throw createError("Email already exists", 400);

            const defaultPassword = generateDefaultPassword(
                memberData.date_of_birth
            );
            const hashedPassword = await hashPassword(defaultPassword);

            const member = await prisma.member.create({
                data: {
                    ...memberData,
                    password_hash: hashedPassword,
                    password_reset_required: true,
                    date_of_birth: memberData.date_of_birth
                        ? new Date(memberData.date_of_birth)
                        : null,
                },
            });

            res.status(201).json({ success: true, data: member });
        } catch (error) {
            next(error);
        }
    }

    async updateMember(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;
            const updateData = req.body;

            // Clean up data before update
            const cleanedData = { ...updateData };

            // Handle date_of_birth
            if (cleanedData.date_of_birth) {
                cleanedData.date_of_birth = new Date(cleanedData.date_of_birth);
            } else {
                delete cleanedData.date_of_birth;
            }

            // Handle cell_group_id - set to null if empty string or undefined
            if (
                cleanedData.cell_group_id === "" ||
                cleanedData.cell_group_id === undefined
            ) {
                cleanedData.cell_group_id = null;
            }

            // Handle district_id - set to null if empty string or undefined
            if (
                cleanedData.district_id === "" ||
                cleanedData.district_id === undefined
            ) {
                cleanedData.district_id = null;
            }

            // Validate cell_group_id exists if provided
            if (cleanedData.cell_group_id) {
                const cellGroupExists = await prisma.cellGroup.findUnique({
                    where: { id: cleanedData.cell_group_id },
                });
                if (!cellGroupExists) {
                    return res.status(400).json({
                        success: false,
                        error: { message: "Cell group not found" },
                    });
                }
            }

            // Validate district_id exists if provided
            if (cleanedData.district_id) {
                const districtExists = await prisma.district.findUnique({
                    where: { id: cleanedData.district_id },
                });
                if (!districtExists) {
                    return res.status(400).json({
                        success: false,
                        error: { message: "District not found" },
                    });
                }
            }

            const member = await prisma.member.update({
                where: { id },
                data: cleanedData,
                include: {
                    cell_group: true,
                    district: true,
                },
            });

            res.json({ success: true, data: member });
        } catch (error) {
            next(error);
        }
    }

    async deleteMember(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;
            await prisma.member.update({
                where: { id },
                data: { status: "inactive" },
            });
            res.json({
                success: true,
                data: { message: "Member deleted successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async getMemberAttendance(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;
            const {
                page = 1,
                limit = 50,
                event_category,
                start_date,
                end_date,
                timeFilter,
            }: {
                page?: number;
                limit?: number;
                event_category?: string;
                start_date?: string;
                end_date?: string;
                timeFilter?: string;
            } = req.query;

            // Build date filter for meeting
            const dateFilter: any = {};
            if (timeFilter) {
                const now = new Date();
                switch (timeFilter) {
                    case "week":
                        const weekAgo = new Date(now);
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        dateFilter.gte = weekAgo;
                        break;
                    case "month":
                        const monthAgo = new Date(now);
                        monthAgo.setMonth(monthAgo.getMonth() - 1);
                        dateFilter.gte = monthAgo;
                        break;
                    case "3months":
                        const threeMonthsAgo = new Date(now);
                        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                        dateFilter.gte = threeMonthsAgo;
                        break;
                    case "year":
                        const yearAgo = new Date(now);
                        yearAgo.setFullYear(yearAgo.getFullYear() - 1);
                        dateFilter.gte = yearAgo;
                        break;
                    // 'all' or default - no date filter
                }
            }

            if (start_date && end_date) {
                dateFilter.gte = new Date(start_date);
                dateFilter.lte = new Date(end_date);
            } else if (start_date) {
                dateFilter.gte = new Date(start_date);
            } else if (end_date) {
                dateFilter.lte = new Date(end_date);
            }

            // Build where clause for meeting filter
            const meetingWhere: any = {};
            if (Object.keys(dateFilter).length > 0) {
                meetingWhere.meeting_date = dateFilter;
            }
            if (event_category) {
                meetingWhere.event_category = event_category;
            }

            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);

            const attendance = await prisma.attendanceParticipant.findMany({
                where: {
                    member_id: id,
                    ...(Object.keys(meetingWhere).length > 0
                        ? { meeting: meetingWhere }
                        : {}),
                },
                skip,
                take,
                include: {
                    meeting: {
                        select: {
                            id: true,
                            meeting_date: true,
                            event_category: true,
                            meeting_type: true,
                            topic: true,
                            location: true,
                            cell_group: { select: { name: true } },
                        },
                    },
                },
                orderBy: { meeting: { meeting_date: "desc" } },
            });

            // Get total count
            const totalCount = await prisma.attendanceParticipant.count({
                where: {
                    member_id: id,
                    ...(Object.keys(meetingWhere).length > 0
                        ? { meeting: meetingWhere }
                        : {}),
                },
            });

            // Format response to match expected structure
            const formattedAttendance = attendance.map((record) => ({
                id: record.id,
                event_date: record.meeting.meeting_date,
                event_type: record.meeting.event_category,
                status: record.status,
                meeting: {
                    meeting_date: record.meeting.meeting_date,
                    event_category: record.meeting.event_category,
                    topic: record.meeting.topic,
                    location: record.meeting.location,
                    cell_group_name: record.meeting.cell_group?.name,
                },
            }));

            res.json({
                success: true,
                data: formattedAttendance,
                pagination: {
                    page,
                    limit,
                    total: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                },
            });
        } catch (error) {
            console.error("Error in getMemberAttendance:", error);
            next(error);
        }
    }

    async setMemberPassword(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;
            const { password } = req.body;
            const hashedPassword = await hashPassword(password);

            await prisma.member.update({
                where: { id },
                data: {
                    password_hash: hashedPassword,
                    password_reset_required: true,
                },
            });

            res.json({
                success: true,
                data: { message: "Password set successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async updateMemberProfile(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const currentUser = req.user;

            // Ensure member can only update their own profile
            if (currentUser?.id !== id && currentUser?.role !== "admin") {
                throw createError(
                    "Unauthorized: You can only update your own profile",
                    403
                );
            }

            // Clean up data before update - only allow specific fields for self-update
            const allowedFields = [
                "first_name",
                "last_name",
                "phone",
                "address",
                "date_of_birth",
                "gender",
                "marital_status",
                "occupation",
                "emergency_contact_name",
                "emergency_contact_phone",
            ];

            const cleanedData: any = {};
            for (const field of allowedFields) {
                if (updateData[field] !== undefined) {
                    cleanedData[field] = updateData[field];
                }
            }

            // Handle date_of_birth
            if (cleanedData.date_of_birth) {
                cleanedData.date_of_birth = new Date(cleanedData.date_of_birth);
            }

            const member = await prisma.member.update({
                where: { id },
                data: cleanedData,
                include: {
                    cell_group: {
                        select: {
                            id: true,
                            name: true,
                            district: {
                                select: { id: true, name: true },
                            },
                        },
                    },
                    district: { select: { id: true, name: true } },
                },
            });

            res.json({ success: true, data: member });
        } catch (error) {
            next(error);
        }
    }
}
