import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { AuthRequest } from "../middleware/auth";
import { ApiResponse, PaginationQuery } from "../types/api";

export class ClassController {
    async getClasses(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const {
                page = 1,
                limit = 10,
                search,
                category,
                status,
            }: PaginationQuery & {
                category?: string;
                status?: string;
            } = req.query;

            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);

            const where: any = {};

            if (search) {
                where.OR = [
                    { name: { contains: search } },
                    { description: { contains: search } },
                ];
            }

            if (category) {
                where.category = category;
            }

            if (status) {
                where.status = status;
            }

            const [classes, total] = await Promise.all([
                prisma.class.findMany({
                    where,
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        category: true,
                        max_students: true,
                        status: true,
                        has_levels: true,
                        created_at: true,
                        _count: {
                            select: {
                                levels: true,
                                enrollments: { where: { status: "enrolled" } },
                                sessions: true,
                            },
                        },
                    },
                    orderBy: { name: "asc" },
                    skip,
                    take,
                }),
                prisma.class.count({ where }),
            ]);

            res.json({
                success: true,
                data: classes,
                pagination: {
                    page,
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        } catch (error) {
            next(error);
        }
    }

    async getClassById(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;

            const classData = await prisma.class.findUnique({
                where: { id },
                include: {
                    levels: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            order_number: true,
                            topics: {
                                select: {
                                    id: true,
                                    name: true,
                                    description: true,
                                    order_number: true,
                                    duration_minutes: true,
                                },
                                orderBy: { order_number: "asc" },
                            },
                            _count: {
                                select: {
                                    enrollments: {
                                        where: { status: "enrolled" },
                                    },
                                },
                            },
                        },
                        orderBy: { order_number: "asc" },
                    },
                    sessions: {
                        select: {
                            id: true,
                            title: true,
                            session_date: true,
                            start_time: true,
                            end_time: true,
                            location: true,
                            order_number: true,
                            instructor: {
                                select: { first_name: true, last_name: true },
                            },
                        },
                        orderBy: { order_number: "asc" },
                    },
                    _count: {
                        select: {
                            enrollments: { where: { status: "enrolled" } },
                        },
                    },
                },
            });

            if (!classData) {
                throw createError("Class not found", 404);
            }

            // Transform data to match frontend expectations
            const transformedData = {
                ...classData,
                levels: classData.levels.map((level) => ({
                    id: level.id,
                    name: level.name,
                    description: level.description,
                    order: level.order_number,
                    topics: level.topics,
                })),
            };

            res.json({ success: true, data: transformedData });
        } catch (error) {
            next(error);
        }
    }

    async createClass(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { levels, sessions, ...classData } = req.body;

            // Prepare class data with proper date handling
            const preparedClassData = { ...classData };

            // Handle date fields - convert empty strings to null
            if (
                preparedClassData.start_date === "" ||
                preparedClassData.start_date === undefined
            ) {
                preparedClassData.start_date = null;
            } else if (preparedClassData.start_date) {
                preparedClassData.start_date = new Date(
                    preparedClassData.start_date
                );
            }

            if (
                preparedClassData.end_date === "" ||
                preparedClassData.end_date === undefined
            ) {
                preparedClassData.end_date = null;
            } else if (preparedClassData.end_date) {
                preparedClassData.end_date = new Date(
                    preparedClassData.end_date
                );
            }

            // Create the class first
            const newClass = await prisma.class.create({
                data: preparedClassData,
            });

            // Handle levels and topics for multi-level classes
            if (classData.has_levels && levels && Array.isArray(levels)) {
                for (const levelData of levels) {
                    const createdLevel = await prisma.classLevel.create({
                        data: {
                            class_id: newClass.id,
                            name: levelData.name,
                            description: levelData.description || "",
                            order_number: levelData.order || 1,
                        },
                    });

                    // Create topics for this level
                    if (levelData.topics && Array.isArray(levelData.topics)) {
                        for (const topicData of levelData.topics) {
                            await prisma.classTopic.create({
                                data: {
                                    level_id: createdLevel.id,
                                    name: topicData.name,
                                    description: topicData.description || "",
                                    order_number: topicData.order || 1,
                                    duration_minutes:
                                        topicData.duration_minutes || 60,
                                },
                            });
                        }
                    }
                }
            }

            // Handle sessions for single-level classes
            if (!classData.has_levels && sessions && Array.isArray(sessions)) {
                for (const sessionData of sessions) {
                    await prisma.classSession.create({
                        data: {
                            class_id: newClass.id,
                            title: sessionData.title,
                            description: sessionData.description || "",
                            order_number: sessionData.order || 1,
                            session_date: new Date(), // Placeholder date
                            start_time: "00:00",
                            end_time: "01:00",
                        },
                    });
                }
            }

            // Fetch the created class with its relations
            const classWithRelations = await prisma.class.findUnique({
                where: { id: newClass.id },
                include: {
                    levels: {
                        include: {
                            topics: true,
                        },
                        orderBy: { order_number: "asc" },
                    },
                    sessions: {
                        orderBy: { order_number: "asc" },
                    },
                },
            });

            res.status(201).json({ success: true, data: classWithRelations });
        } catch (error) {
            next(error);
        }
    }

    async updateClass(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;
            const { levels, sessions, ...classData } = req.body;

            console.log("🔄 Updating class:", id);
            console.log("📝 Class data:", JSON.stringify(classData, null, 2));
            console.log("📚 Has levels:", classData.has_levels);
            console.log("🔢 Levels count:", levels ? levels.length : 0);
            console.log("📖 Sessions count:", sessions ? sessions.length : 0);

            // Prepare class data with proper date handling
            const preparedClassData = { ...classData };

            // Handle date fields - convert empty strings to null
            if (
                preparedClassData.start_date === "" ||
                preparedClassData.start_date === undefined
            ) {
                preparedClassData.start_date = null;
            } else if (preparedClassData.start_date) {
                preparedClassData.start_date = new Date(
                    preparedClassData.start_date
                );
            }

            if (
                preparedClassData.end_date === "" ||
                preparedClassData.end_date === undefined
            ) {
                preparedClassData.end_date = null;
            } else if (preparedClassData.end_date) {
                preparedClassData.end_date = new Date(
                    preparedClassData.end_date
                );
            }

            console.log(
                "🔧 Prepared class data:",
                JSON.stringify(preparedClassData, null, 2)
            );

            // Update class data first
            const updatedClass = await prisma.class.update({
                where: { id },
                data: preparedClassData,
            });

            console.log("✅ Class basic data updated");

            // Handle levels for multi-level classes
            if (classData.has_levels && levels && Array.isArray(levels)) {
                console.log(
                    "Updating levels data:",
                    JSON.stringify(levels, null, 2)
                );

                // Get existing levels to handle deletions
                const existingLevels = await prisma.classLevel.findMany({
                    where: { class_id: id },
                    include: { topics: true },
                });

                const levelIdsInRequest = levels
                    .filter((level) => level.id)
                    .map((level) => level.id);

                // Delete levels that are no longer in the request
                const levelsToDelete = existingLevels.filter(
                    (level) => !levelIdsInRequest.includes(level.id)
                );

                for (const levelToDelete of levelsToDelete) {
                    await prisma.classLevel.delete({
                        where: { id: levelToDelete.id },
                    });
                }

                // Update or create levels
                for (let i = 0; i < levels.length; i++) {
                    const levelData = levels[i];
                    console.log(
                        `🔄 Processing level ${i + 1}:`,
                        levelData.name
                    );

                    try {
                        if (levelData.id) {
                            console.log(
                                "✏️ Updating existing level:",
                                levelData.id
                            );
                            // Update existing level
                            const updatedLevel = await prisma.classLevel.update(
                                {
                                    where: { id: levelData.id },
                                    data: {
                                        name: levelData.name,
                                        description:
                                            levelData.description || "",
                                        order_number:
                                            levelData.order ||
                                            levelData.order_number ||
                                            i + 1,
                                    },
                                }
                            );

                            console.log("✅ Level updated successfully");

                            // Handle topics for this level
                            if (
                                levelData.topics &&
                                Array.isArray(levelData.topics)
                            ) {
                                console.log(
                                    `📚 Processing ${levelData.topics.length} topics for level`
                                );

                                // Delete existing topics for this level
                                const deletedTopics =
                                    await prisma.classTopic.deleteMany({
                                        where: { level_id: updatedLevel.id },
                                    });
                                console.log(
                                    `🗑️ Deleted ${deletedTopics.count} existing topics`
                                );

                                // Create new topics
                                for (
                                    let j = 0;
                                    j < levelData.topics.length;
                                    j++
                                ) {
                                    const topicData = levelData.topics[j];
                                    console.log(
                                        `📝 Creating topic ${j + 1}: ${
                                            topicData.name
                                        }`
                                    );

                                    if (
                                        !topicData.name ||
                                        topicData.name.trim() === ""
                                    ) {
                                        console.log("⚠️ Skipping empty topic");
                                        continue;
                                    }

                                    await prisma.classTopic.create({
                                        data: {
                                            level_id: updatedLevel.id,
                                            name: topicData.name,
                                            description:
                                                topicData.description || "",
                                            order_number:
                                                topicData.order || j + 1,
                                            duration_minutes:
                                                topicData.duration_minutes ||
                                                60,
                                        },
                                    });
                                    console.log(
                                        "✅ Topic created successfully"
                                    );
                                }
                            }
                        } else {
                            console.log("➕ Creating new level");
                            // Create new level
                            const newLevel = await prisma.classLevel.create({
                                data: {
                                    class_id: id,
                                    name: levelData.name,
                                    description: levelData.description || "",
                                    order_number: levelData.order || i + 1,
                                },
                            });

                            console.log("✅ New level created successfully");

                            // Create topics for new level
                            if (
                                levelData.topics &&
                                Array.isArray(levelData.topics)
                            ) {
                                console.log(
                                    `📚 Creating ${levelData.topics.length} topics for new level`
                                );

                                for (
                                    let j = 0;
                                    j < levelData.topics.length;
                                    j++
                                ) {
                                    const topicData = levelData.topics[j];
                                    console.log(
                                        `📝 Creating topic ${j + 1}: ${
                                            topicData.name
                                        }`
                                    );

                                    if (
                                        !topicData.name ||
                                        topicData.name.trim() === ""
                                    ) {
                                        console.log("⚠️ Skipping empty topic");
                                        continue;
                                    }

                                    await prisma.classTopic.create({
                                        data: {
                                            level_id: newLevel.id,
                                            name: topicData.name,
                                            description:
                                                topicData.description || "",
                                            order_number:
                                                topicData.order || j + 1,
                                            duration_minutes:
                                                topicData.duration_minutes ||
                                                60,
                                        },
                                    });
                                    console.log(
                                        "✅ Topic created successfully"
                                    );
                                }
                            }
                        }
                    } catch (levelError) {
                        console.error(
                            `❌ Error processing level ${i + 1}:`,
                            levelError
                        );
                        const errorMessage =
                            levelError instanceof Error
                                ? levelError.message
                                : String(levelError);
                        throw new Error(
                            `Failed to process level "${levelData.name}": ${errorMessage}`
                        );
                    }
                }

                console.log("✅ All levels processed successfully");
            } else if (
                !classData.has_levels &&
                sessions &&
                Array.isArray(sessions)
            ) {
                console.log(
                    "Updating sessions data:",
                    JSON.stringify(sessions, null, 2)
                );

                // Delete existing sessions for single-level classes
                await prisma.classSession.deleteMany({
                    where: { class_id: id },
                });

                // Create new sessions
                for (const sessionData of sessions) {
                    await prisma.classSession.create({
                        data: {
                            class_id: id,
                            title: sessionData.title,
                            description: sessionData.description || "",
                            order_number: sessionData.order || 1,
                            session_date: new Date(), // Placeholder date
                            start_time: "00:00",
                            end_time: "01:00",
                        },
                    });
                }
            }

            // If switching from multi-level to single-level, clean up levels
            if (!classData.has_levels) {
                await prisma.classLevel.deleteMany({
                    where: { class_id: id },
                });
            }

            // If switching from single-level to multi-level, clean up sessions
            if (classData.has_levels) {
                await prisma.classSession.deleteMany({
                    where: { class_id: id },
                });
            }

            // Return updated class with all relations
            const classWithRelations = await prisma.class.findUnique({
                where: { id },
                include: {
                    levels: {
                        include: {
                            topics: {
                                orderBy: { order_number: "asc" },
                            },
                        },
                        orderBy: { order_number: "asc" },
                    },
                    sessions: {
                        orderBy: { order_number: "asc" },
                    },
                    _count: {
                        select: {
                            enrollments: { where: { status: "enrolled" } },
                        },
                    },
                },
            });

            res.json({ success: true, data: classWithRelations });
        } catch (error) {
            console.error("Error updating class:", error);
            next(error);
        }
    }

    async deleteClass(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;

            await prisma.class.delete({
                where: { id },
            });

            res.json({
                success: true,
                data: { message: "Class deleted successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async getClassLevels(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;

            const levels = await prisma.classLevel.findMany({
                where: { class_id: id },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    order_number: true,
                    prerequisite_level: {
                        select: { id: true, name: true },
                    },
                    _count: {
                        select: {
                            enrollments: { where: { status: "enrolled" } },
                        },
                    },
                },
                orderBy: { order_number: "asc" },
            });

            res.json({ success: true, data: levels });
        } catch (error) {
            next(error);
        }
    }

    async createClassLevel(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const levelData = { ...req.body, class_id: id };

            const level = await prisma.classLevel.create({
                data: levelData,
            });

            res.status(201).json({ success: true, data: level });
        } catch (error) {
            next(error);
        }
    }

    async getClassSessions(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;
            const { level_id } = req.query;

            // First, check if this is a multi-level class
            const classInfo = await prisma.class.findUnique({
                where: { id },
                select: { has_levels: true },
            });

            if (!classInfo) {
                throw createError("Class not found", 404);
            }

            let sessions: any[] = [];

            if (classInfo.has_levels && level_id) {
                // For multi-level classes, get topics from class_topics table
                const topics = await prisma.classTopic.findMany({
                    where: { level_id: level_id as string },
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        order_number: true,
                        duration_minutes: true,
                        level: {
                            select: { id: true, name: true },
                        },
                    },
                    orderBy: { order_number: "asc" },
                });

                // Transform topics to session format for frontend consistency
                sessions = topics.map((topic) => ({
                    id: topic.id,
                    title: topic.name,
                    description: topic.description,
                    session_date: null,
                    start_time: null,
                    end_time: null,
                    location: null,
                    order_number: topic.order_number,
                    duration_minutes: topic.duration_minutes,
                    instructor: null,
                    level: topic.level,
                }));
            } else {
                // For single-level classes, get actual sessions from class_sessions table
                const where: any = { class_id: id };

                if (level_id) {
                    where.level_id = level_id;
                }

                sessions = await prisma.classSession.findMany({
                    where,
                    select: {
                        id: true,
                        title: true,
                        description: true,
                        session_date: true,
                        start_time: true,
                        end_time: true,
                        location: true,
                        order_number: true,
                        instructor: {
                            select: {
                                id: true,
                                first_name: true,
                                last_name: true,
                            },
                        },
                        level: {
                            select: { id: true, name: true },
                        },
                    },
                    orderBy: { order_number: "asc" },
                });
            }

            res.json({ success: true, data: sessions });
        } catch (error) {
            next(error);
        }
    }
    async createClassSession(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const sessionData = {
                ...req.body,
                class_id: id,
                session_date: new Date(req.body.session_date),
            };

            const session = await prisma.classSession.create({
                data: sessionData,
            });

            res.status(201).json({ success: true, data: session });
        } catch (error) {
            next(error);
        }
    }

    async getClassEnrollments(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;

            const enrollments = await prisma.classEnrollment.findMany({
                where: { class_id: id },
                select: {
                    id: true,
                    status: true,
                    enrolled_at: true,
                    completed_at: true,
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                        },
                    },
                    level: {
                        select: { id: true, name: true },
                    },
                },
                orderBy: { enrolled_at: "desc" },
            });

            res.json({ success: true, data: enrollments });
        } catch (error) {
            next(error);
        }
    }

    async enrollMember(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const { member_id, level_id } = req.body;

            // Check if member is already enrolled
            const existingEnrollment = await prisma.classEnrollment.findUnique({
                where: {
                    class_id_member_id: {
                        class_id: id,
                        member_id,
                    },
                },
            });

            if (existingEnrollment) {
                throw createError(
                    "Member is already enrolled in this class",
                    400
                );
            }

            const enrollment = await prisma.classEnrollment.create({
                data: {
                    class_id: id,
                    member_id,
                    level_id,
                },
            });

            res.status(201).json({ success: true, data: enrollment });
        } catch (error) {
            next(error);
        }
    }

    // Self-enrollment method for members (uses authenticated user's ID)
    async selfEnrollMember(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const { level_id } = req.body;
            const member_id = req.user?.id; // Get member ID from authenticated token

            if (!member_id) {
                throw createError("Authentication required", 401);
            }

            // Check if class exists and is active
            const classData = await prisma.class.findUnique({
                where: { id },
                select: { id: true, status: true, name: true },
            });

            if (!classData) {
                throw createError("Class not found", 404);
            }

            if (classData.status !== "active") {
                throw createError("Class is not available for enrollment", 400);
            }

            // Check if member is already enrolled
            const existingEnrollment = await prisma.classEnrollment.findUnique({
                where: {
                    class_id_member_id: {
                        class_id: id,
                        member_id,
                    },
                },
            });

            if (existingEnrollment) {
                throw createError(
                    "You are already enrolled in this class",
                    400
                );
            }

            const enrollment = await prisma.classEnrollment.create({
                data: {
                    class_id: id,
                    member_id,
                    level_id,
                },
                include: {
                    class: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                        },
                    },
                },
            });

            res.status(201).json({
                success: true,
                data: enrollment,
                message: `Successfully enrolled in ${classData.name}`,
            });
        } catch (error) {
            next(error);
        }
    }

    async updateEnrollment(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { enrollmentId } = req.params;
            const { status } = req.body;

            const updateData: any = { status };
            if (status === "completed") {
                updateData.completion_date = new Date();
            }

            const enrollment = await prisma.classEnrollment.update({
                where: { id: enrollmentId },
                data: updateData,
            });

            res.json({ success: true, data: enrollment });
        } catch (error) {
            next(error);
        }
    }

    async getLevelById(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { levelId } = req.params;

            const level = await prisma.classLevel.findUnique({
                where: { id: levelId },
                include: {
                    class: { select: { id: true, name: true } },
                    prerequisite_level: { select: { id: true, name: true } },
                    sessions: {
                        select: {
                            id: true,
                            title: true,
                            session_date: true,
                            start_time: true,
                            end_time: true,
                        },
                        orderBy: { order_number: "asc" },
                    },
                },
            });

            if (!level) {
                throw createError("Level not found", 404);
            }

            res.json({ success: true, data: level });
        } catch (error) {
            next(error);
        }
    }

    async updateLevel(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { levelId } = req.params;
            const updateData = req.body;

            const level = await prisma.classLevel.update({
                where: { id: levelId },
                data: updateData,
            });

            res.json({ success: true, data: level });
        } catch (error) {
            next(error);
        }
    }

    async deleteLevel(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { levelId } = req.params;

            await prisma.classLevel.delete({
                where: { id: levelId },
            });

            res.json({
                success: true,
                data: { message: "Level deleted successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async getSessionById(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { sessionId } = req.params;

            const session = await prisma.classSession.findUnique({
                where: { id: sessionId },
                include: {
                    class: { select: { id: true, name: true } },
                    level: { select: { id: true, name: true } },
                    instructor: {
                        select: { id: true, first_name: true, last_name: true },
                    },
                    attendance_meeting: {
                        select: {
                            id: true,
                            _count: { select: { participants: true } },
                        },
                    },
                },
            });

            if (!session) {
                throw createError("Session not found", 404);
            }

            res.json({ success: true, data: session });
        } catch (error) {
            next(error);
        }
    }

    async updateSession(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { sessionId } = req.params;
            const updateData = req.body;

            if (updateData.session_date) {
                updateData.session_date = new Date(updateData.session_date);
            }

            const session = await prisma.classSession.update({
                where: { id: sessionId },
                data: updateData,
            });

            res.json({ success: true, data: session });
        } catch (error) {
            next(error);
        }
    }

    async deleteSession(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { sessionId } = req.params;

            await prisma.classSession.delete({
                where: { id: sessionId },
            });

            res.json({
                success: true,
                data: { message: "Session deleted successfully" },
            });
        } catch (error) {
            next(error);
        }
    }
}
