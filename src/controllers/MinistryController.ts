import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { AuthRequest } from "../middleware/auth";
import { ApiResponse, PaginationQuery } from "../types/api";
import { MinistryRoleController } from "./MinistryRoleController";

export class MinistryController {
    async getMinistries(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { page = 1, limit = 10, search }: PaginationQuery = req.query;

            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);

            const where: any = { status: "active" };

            if (search) {
                where.OR = [
                    { name: { contains: search } },
                    { description: { contains: search } },
                ];
            }

            const [ministries, total] = await Promise.all([
                prisma.ministry.findMany({
                    where,
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        status: true,
                        created_at: true,
                        leader: {
                            select: {
                                id: true,
                                first_name: true,
                                last_name: true,
                            },
                        },
                        _count: {
                            select: {
                                ministry_members: {
                                    where: { status: "active" },
                                },
                            },
                        },
                    },
                    orderBy: { name: "asc" },
                    skip,
                    take,
                }),
                prisma.ministry.count({ where }),
            ]);

            res.json({
                success: true,
                data: ministries,
                pagination: {
                    page,
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        } catch (error) {
            next(error);
        }
    }

    async getMinistryById(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;

            const ministry = await prisma.ministry.findUnique({
                where: { id },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    status: true,
                    created_at: true,
                    updated_at: true,
                    leader: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                        },
                    },
                    ministry_members: {
                        select: {
                            id: true,
                            role: true,
                            joined_date: true,
                            status: true,
                            member: {
                                select: {
                                    id: true,
                                    first_name: true,
                                    last_name: true,
                                    email: true,
                                    phone: true,
                                },
                            },
                        },
                        where: { status: "active" },
                        orderBy: { member: { last_name: "asc" } },
                    },
                    _count: {
                        select: {
                            ministry_members: { where: { status: "active" } },
                            attendance_meetings: true,
                        },
                    },
                },
            });

            if (!ministry) {
                throw createError("Ministry not found", 404);
            }

            res.json({ success: true, data: ministry });
        } catch (error) {
            next(error);
        }
    }

    async createMinistry(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const ministryData = req.body;

            // Validate leader exists if provided
            if (ministryData.leader_id) {
                const leader = await prisma.member.findUnique({
                    where: { id: ministryData.leader_id },
                });
                if (!leader) {
                    throw createError("Leader not found", 400);
                }
            }

            const ministry = await prisma.ministry.create({
                data: ministryData,
            });

            res.status(201).json({ success: true, data: ministry });
        } catch (error) {
            next(error);
        }
    }

    async updateMinistry(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;
            const updateData = req.body;

            const ministry = await prisma.ministry.update({
                where: { id },
                data: updateData,
            });

            res.json({ success: true, data: ministry });
        } catch (error) {
            next(error);
        }
    }

    async deleteMinistry(req: AuthRequest, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;

            await prisma.ministry.update({
                where: { id },
                data: { status: "inactive" },
            });

            res.json({
                success: true,
                data: { message: "Ministry deleted successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async getMinistryMembers(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;

            const members = await prisma.ministryMember.findMany({
                where: {
                    ministry_id: id,
                    status: "active",
                },
                select: {
                    id: true,
                    role: true,
                    joined_date: true,
                    status: true,
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                            status: true,
                        },
                    },
                },
                orderBy: { member: { last_name: "asc" } },
            });

            res.json({ success: true, data: members });
        } catch (error) {
            next(error);
        }
    }

    async addMembersToMinistry(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id } = req.params;
            const { member_ids, role } = req.body;

            // Check if ministry exists
            const ministry = await prisma.ministry.findUnique({
                where: { id },
            });

            if (!ministry) {
                throw createError("Ministry not found", 404);
            }

            // Auto-create role if it doesn't exist and role is provided
            if (role && typeof role === "string" && role.trim()) {
                await MinistryRoleController.autoCreateRoleIfNeeded(
                    role.trim()
                );
            }

            // Create ministry memberships
            const memberships = member_ids.map((member_id: string) => ({
                ministry_id: id,
                member_id,
                role: role || null,
            }));

            await prisma.ministryMember.createMany({
                data: memberships,
                skipDuplicates: true,
            });

            res.json({
                success: true,
                data: { message: "Members added successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async updateMinistryMember(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                throw createError("Validation failed", 400);
            }

            const { id, memberId } = req.params;
            const updateData = req.body;

            const membership = await prisma.ministryMember.updateMany({
                where: {
                    ministry_id: id,
                    member_id: memberId,
                },
                data: updateData,
            });

            if (membership.count === 0) {
                throw createError("Ministry membership not found", 404);
            }

            res.json({
                success: true,
                data: { message: "Ministry member updated successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async removeMemberFromMinistry(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id, memberId } = req.params;

            await prisma.ministryMember.updateMany({
                where: {
                    ministry_id: id,
                    member_id: memberId,
                },
                data: { status: "inactive" },
            });

            res.json({
                success: true,
                data: { message: "Member removed successfully" },
            });
        } catch (error) {
            next(error);
        }
    }

    async getMinistryMeetings(
        req: AuthRequest,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;
            const { page = 1, limit = 10 } = req.query;

            const skip = (Number(page) - 1) * Number(limit);
            const take = Math.min(Number(limit), 100);

            const [meetings, total] = await Promise.all([
                prisma.attendanceMeeting.findMany({
                    where: {
                        ministry_id: id,
                        event_category: "ministry",
                    },
                    select: {
                        id: true,
                        meeting_date: true,
                        meeting_type: true,
                        topic: true,
                        notes: true,
                        location: true,
                        offering: true,
                        is_realtime: true,
                        created_at: true,
                        _count: {
                            select: {
                                participants: true,
                                visitors: true,
                            },
                        },
                    },
                    orderBy: { meeting_date: "desc" },
                    skip,
                    take,
                }),
                prisma.attendanceMeeting.count({
                    where: {
                        ministry_id: id,
                        event_category: "ministry",
                    },
                }),
            ]);

            res.json({
                success: true,
                data: meetings,
                pagination: {
                    page: Number(page),
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        } catch (error) {
            next(error);
        }
    }
}
