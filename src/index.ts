import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import compression from "compression";
import rateLimit from "express-rate-limit";
import { createServer } from "http";
import { Server } from "socket.io";
import "express-async-errors";

import { config } from "./config/config";
import { errorHandler } from "./middleware/errorHandler";
import { notFoundHandler } from "./middleware/notFoundHandler";

// Import routes
import authRoutes from "./routes/auth";
import memberRoutes from "./routes/members";
import cellGroupRoutes from "./routes/cellGroups";
import districtRoutes from "./routes/districts";
import attendanceRoutes from "./routes/attendance";
import classRoutes from "./routes/classes";
import ministryRoutes from "./routes/ministries";
import ministryRoleRoutes from "./routes/ministryRoles";
import articleRoutes from "./routes/articles";
import articleCategoryRoutes from "./routes/articleCategories";
import enrollmentRoutes from "./routes/enrollment";

const app = express();
const server = createServer(app);
const io = new Server(server, {
    cors: {
        origin: config.FRONTEND_URL,
        methods: ["GET", "POST"],
    },
});

// Rate limiting
const limiter = rateLimit({
    windowMs: config.RATE_LIMIT_WINDOW_MS,
    max: config.RATE_LIMIT_MAX_REQUESTS,
    message: "Too many requests from this IP, please try again later.",
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(
    cors({
        origin: config.FRONTEND_URL,
        credentials: true,
    })
);
app.use(morgan("combined"));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get("/health", (req, res) => {
    res.json({ status: "OK", timestamp: new Date().toISOString() });
});

// API Routes
app.use("/api/auth", authRoutes);
app.use("/api/members", memberRoutes);
app.use("/api/cell-groups", cellGroupRoutes);
app.use("/api/districts", districtRoutes);
app.use("/api/attendance", attendanceRoutes);
app.use("/api/classes", classRoutes);
app.use("/api/ministries", ministryRoutes);
app.use("/api/ministry-roles", ministryRoleRoutes);
app.use("/api/articles", articleRoutes);
app.use("/api/article-categories", articleCategoryRoutes);
app.use("/api", enrollmentRoutes);
app.use("/api/projects", require("./routes/projects").default);
app.use("/api/donations", require("./routes/donations").default);

// Socket.IO for real-time features
io.on("connection", (socket) => {
    console.log("Client connected:", socket.id);

    socket.on("join-meeting", (meetingId) => {
        socket.join(`meeting-${meetingId}`);
        console.log(`Client ${socket.id} joined meeting ${meetingId}`);
    });

    socket.on("leave-meeting", (meetingId) => {
        socket.leave(`meeting-${meetingId}`);
        console.log(`Client ${socket.id} left meeting ${meetingId}`);
    });

    socket.on("disconnect", () => {
        console.log("Client disconnected:", socket.id);
    });
});

// Store io instance globally for use in other modules
app.set("io", io);

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

const PORT = config.PORT || 3001;
const HOST = "0.0.0.0"; // Listen on all network interfaces

server.listen(PORT, HOST, () => {
    console.log(`🚀 Server running on http://${HOST}:${PORT}`);
    console.log(`📊 Environment: ${config.NODE_ENV}`);
    console.log(`🔗 Frontend URL: ${config.FRONTEND_URL}`);
    console.log(`🌐 Accessible from network: http://*************:${PORT}`);
});

export { app, io };
