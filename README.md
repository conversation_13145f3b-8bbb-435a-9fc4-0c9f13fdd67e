# Church Management API

Node.js backend API untuk Church Management System yang menggantikan Supabase dengan MySQL dan Express.js.

## 🚀 Features

- **Authentication System**: JWT-based auth untuk admin dan member
- **Member Management**: CRUD operations untuk member gereja
- **Cell Groups**: Manajemen kelompok sel dengan leader dan member
- **Districts**: Manajemen wilayah gereja
- **Attendance System**: Tracking kehadiran dengan real-time features
- **Classes**: Sistem kelas dengan levels dan sessions
- **Ministries**: Manajemen pelayanan gereja
- **Articles**: Content management system
- **Real-time**: Socket.IO untuk fitur real-time

## 📋 Prerequisites

- Node.js >= 18.0.0
- MySQL 8.0+
- npm atau yarn

## 🛠️ Installation

1. **Clone dan setup project:**
```bash
cd /Users/<USER>/nodejs/church-management-api
npm install
```

2. **Setup environment variables:**
```bash
cp .env.example .env
# Edit .env file dengan konfigurasi database dan JWT secret
```

3. **Setup database:**
```bash
# Buat database MySQL
mysql -u root -p
CREATE DATABASE church_management;

# Push schema ke database
npx prisma db push

# Generate Prisma client
npx prisma generate
```

4. **Run development server:**
```bash
npm run dev
```

5. **Build untuk production:**
```bash
npm run build
npm start
```

## 🔧 Environment Variables

```env
# Database
DATABASE_URL="mysql://username:password@localhost:3306/church_management"

# JWT
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_SECRET="your-refresh-token-secret"
JWT_REFRESH_EXPIRES_IN="30d"

# Server
PORT=3001
NODE_ENV="development"

# CORS
FRONTEND_URL="http://localhost:3000"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
```

## 📚 API Documentation

### Base URL
```
http://localhost:3001/api
```

### Authentication Endpoints

#### Admin Login
```http
POST /api/auth/admin/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Member Login
```http
POST /api/auth/member/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Get Current User
```http
GET /api/auth/me
Authorization: Bearer <token>
```

### Member Endpoints

#### Get All Members
```http
GET /api/members?page=1&limit=10&search=john
Authorization: Bearer <token>
```

#### Get Member by ID
```http
GET /api/members/{id}
Authorization: Bearer <token>
```

#### Create Member
```http
POST /api/members
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890",
  "date_of_birth": "1990-01-01"
}
```

### Cell Groups Endpoints

#### Get All Cell Groups
```http
GET /api/cell-groups?page=1&limit=10
Authorization: Bearer <token>
```

#### Create Cell Group
```http
POST /api/cell-groups
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Youth Cell Group",
  "description": "Cell group for young adults",
  "district_id": "uuid",
  "leader_id": "uuid",
  "meeting_day": "friday",
  "meeting_time": "19:00"
}
```

### Attendance Endpoints

#### Get All Meetings
```http
GET /api/attendance/meetings?page=1&limit=10&event_category=cell_group
Authorization: Bearer <token>
```

#### Create Meeting with Attendance
```http
POST /api/attendance/meetings
Authorization: Bearer <token>
Content-Type: application/json

{
  "event_category": "cell_group",
  "meeting_date": "2024-01-15T19:00:00Z",
  "meeting_type": "Weekly Meeting",
  "topic": "Prayer and Fellowship",
  "cell_group_id": "uuid",
  "participants": [
    {
      "member_id": "uuid",
      "status": "present"
    }
  ],
  "visitors": [
    {
      "name": "Jane Smith",
      "phone": "+1234567890"
    }
  ]
}
```

### Classes Endpoints

#### Get All Classes
```http
GET /api/classes?page=1&limit=10&category=bible_study
Authorization: Bearer <token>
```

#### Create Class
```http
POST /api/classes
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Bible Study 101",
  "description": "Introduction to Bible study",
  "category": "bible_study",
  "max_students": 20,
  "has_levels": true
}
```

### Articles Endpoints (Public)

#### Get Published Articles
```http
GET /api/articles?page=1&limit=10&category=news
```

#### Get Article by ID
```http
GET /api/articles/{id}
```

#### Create Article (Admin only)
```http
POST /api/articles
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Church News",
  "summary": "Latest updates from our church",
  "content": "Full article content...",
  "category": "news",
  "status": "published",
  "featured": true
}
```

## �� Authentication

API menggunakan JWT (JSON Web Tokens) untuk authentication. Setelah login, gunakan token di header:

```http
Authorization: Bearer <your-jwt-token>
```

## 🏗️ Database Schema

### Key Tables:
- **members**: User data dan authentication
- **districts**: Wilayah gereja
- **cell_groups**: Kelompok sel
- **ministries**: Pelayanan gereja
- **attendance_meetings**: Meeting/pertemuan
- **attendance_participants**: Kehadiran member
- **classes**: Sistem kelas
- **articles**: Content management

## 🔄 Real-time Features

API mendukung real-time updates menggunakan Socket.IO untuk:
- Live attendance tracking
- Real-time meeting updates

### Socket.IO Events:
```javascript
// Join meeting room
socket.emit('join-meeting', meetingId);

// Listen for attendance updates
socket.on('attendance-updated', (data) => {
  console.log('Attendance updated:', data);
});
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 📦 Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm start           # Start production server
npm run migrate     # Run database migrations
npm run db:generate # Generate Prisma client
npm run db:studio   # Open Prisma Studio
```

## 🔧 Development

### Project Structure:
```
src/
├── config/          # Database dan konfigurasi
├── controllers/     # Request handlers
├── middleware/      # Authentication, validation, error handling
├── routes/          # API routes
├── types/           # TypeScript types
├── utils/           # Helper functions
└── index.ts         # Main application file
```

### Adding New Endpoints:
1. Update Prisma schema jika perlu
2. Create controller di `src/controllers/`
3. Create routes di `src/routes/`
4. Import routes di `src/index.ts`

## 🚀 Deployment

### Production Build:
```bash
npm run build
npm start
```

### Environment:
- Set `NODE_ENV=production`
- Configure production database URL
- Set secure JWT secrets
- Configure CORS for production frontend URL

## 📄 License

MIT License
