const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function testPassword() {
  try {
    const user = await prisma.member.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        password_hash: true
      }
    });
    
    if (user && user.password_hash) {
      const testPassword = '05102012';
      const isValid = await bcrypt.compare(testPassword, user.password_hash);
      console.log('Password test result:', isValid);
      
      if (!isValid) {
        console.log('Trying to create new hash for password:', testPassword);
        const newHash = await bcrypt.hash(testPassword, 10);
        console.log('New hash would be:', newHash.substring(0, 50) + '...');
        console.log('Current hash is:', user.password_hash.substring(0, 50) + '...');
      } else {
        console.log('Password is correct!');
      }
    } else {
      console.log('User not found or no password hash');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPassword();
