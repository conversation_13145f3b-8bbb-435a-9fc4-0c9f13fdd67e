const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMinistryMeetings() {
    try {
        console.log('Testing ministry meetings data...\n');
        
        // Get all ministries
        const ministries = await prisma.ministry.findMany({
            where: { status: 'active' },
            select: { id: true, name: true }
        });
        
        console.log(`Found ${ministries.length} active ministries:`);
        ministries.forEach(ministry => {
            console.log(`- ${ministry.name} (${ministry.id})`);
        });
        console.log('');
        
        // Get attendance meetings for ministries
        const attendanceMeetings = await prisma.attendanceMeeting.findMany({
            where: { 
                event_category: 'ministry',
                ministry_id: { not: null }
            },
            select: {
                id: true,
                ministry_id: true,
                meeting_date: true,
                topic: true,
                ministry: {
                    select: { name: true }
                },
                _count: {
                    select: {
                        participants: true,
                        visitors: true
                    }
                }
            },
            orderBy: { meeting_date: 'desc' }
        });
        
        console.log(`Found ${attendanceMeetings.length} ministry attendance meetings:`);
        attendanceMeetings.forEach(meeting => {
            console.log(`- ${meeting.ministry?.name}: ${meeting.topic} (${meeting.meeting_date.toISOString().split('T')[0]})`);
            console.log(`  Participants: ${meeting._count.participants}, Visitors: ${meeting._count.visitors}`);
        });
        console.log('');
        
        // Check participants for ministry meetings
        if (attendanceMeetings.length > 0) {
            const sampleMeeting = attendanceMeetings[0];
            console.log(`Checking participants for meeting: ${sampleMeeting.topic}`);
            
            const participants = await prisma.attendanceParticipant.findMany({
                where: { meeting_id: sampleMeeting.id },
                select: {
                    status: true,
                    member: {
                        select: {
                            first_name: true,
                            last_name: true
                        }
                    }
                }
            });
            
            console.log(`Participants (${participants.length}):`);
            participants.forEach(p => {
                console.log(`- ${p.member.first_name} ${p.member.last_name}: ${p.status}`);
            });
        }
        
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await prisma.$disconnect();
    }
}

testMinistryMeetings();
