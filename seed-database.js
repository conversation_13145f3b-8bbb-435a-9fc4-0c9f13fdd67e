const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function seedDatabase() {
  try {
    console.log('🌱 Seeding database with initial data...');

    // 1. Create Districts
    console.log('📍 Creating districts...');
    const districtId1 = crypto.randomUUID();
    const districtId2 = crypto.randomUUID();
    const districtId3 = crypto.randomUUID();
    
    const districts = await Promise.all([
      prisma.district.upsert({
        where: { id: districtId1 },
        update: {},
        create: {
          id: districtId1,
          name: 'Central Jakarta',
          description: 'Wilayah Jakarta Pusat',
          status: 'active'
        }
      }),
      prisma.district.upsert({
        where: { id: districtId2 },
        update: {},
        create: {
          id: districtId2,
          name: 'East Jakarta',
          description: 'Wilayah Jakarta Timur',
          status: 'active'
        }
      }),
      prisma.district.upsert({
        where: { id: districtId3 },
        update: {},
        create: {
          id: districtId3,
          name: 'West Jakarta',
          description: 'Wilayah Jakarta Barat',
          status: 'active'
        }
      })
    ]);
    console.log(`✅ Created ${districts.length} districts`);

    // 2. Create Admin User
    console.log('👤 Creating admin user...');
    const adminPassword = await bcrypt.hash('admin123', 12);
    const adminUser = await prisma.member.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password_hash: adminPassword,
        role: 'admin',
        role_level: 5,
        status: 'active'
      },
      create: {
        email: '<EMAIL>',
        first_name: 'Church',
        last_name: 'Administrator',
        password_hash: adminPassword,
        role: 'admin',
        role_level: 5,
        status: 'active',
        district_id: districts[0].id,
        join_date: new Date()
      }
    });
    console.log('✅ Admin user created/updated');

    // 3. Create Sample Members
    console.log('👥 Creating sample members...');
    const memberPassword = await bcrypt.hash('member123', 12);
    const members = await Promise.all([
      prisma.member.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe',
          password_hash: memberPassword,
          phone: '+62812345671',
          role: 'member',
          role_level: 1,
          status: 'active',
          district_id: districts[0].id,
          join_date: new Date('2024-01-15')
        }
      }),
      prisma.member.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Smith',
          password_hash: memberPassword,
          phone: '+62812345672',
          role: 'leader',
          role_level: 3,
          status: 'active',
          district_id: districts[1].id,
          join_date: new Date('2024-02-20')
        }
      }),
      prisma.member.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          first_name: 'Peter',
          last_name: 'Wilson',
          password_hash: memberPassword,
          phone: '+62812345673',
          role: 'leader',
          role_level: 3,
          status: 'active',
          district_id: districts[2].id,
          join_date: new Date('2024-03-10')
        }
      })
    ]);
    console.log(`✅ Created ${members.length} members`);

    // 4. Create Cell Groups
    console.log('🏠 Creating cell groups...');
    
    // Check if cell groups already exist
    let cellGroup1 = await prisma.cellGroup.findFirst({
      where: { name: 'Victory Cell Central' }
    });
    if (!cellGroup1) {
      cellGroup1 = await prisma.cellGroup.create({
        data: {
          name: 'Victory Cell Central',
          description: 'Cell group untuk wilayah Jakarta Pusat',
          district_id: districts[0].id,
          leader_id: members[1].id,
          meeting_day: 'Friday',
          meeting_time: '19:00',
          meeting_location: 'Rumah Jane Smith',
          status: 'active'
        }
      });
    }
    
    let cellGroup2 = await prisma.cellGroup.findFirst({
      where: { name: 'Faith Cell East' }
    });
    if (!cellGroup2) {
      cellGroup2 = await prisma.cellGroup.create({
        data: {
          name: 'Faith Cell East',
          description: 'Cell group untuk wilayah Jakarta Timur',
          district_id: districts[1].id,
          leader_id: members[2].id,
          meeting_day: 'Thursday',
          meeting_time: '19:30',
          meeting_location: 'Rumah Peter Wilson',
          status: 'active'
        }
      });
    }
    
    const cellGroups = [cellGroup1, cellGroup2];
    console.log(`✅ Created/found ${cellGroups.length} cell groups`);

    // 5. Add members to cell groups
    console.log('👨‍👩‍👧‍👦 Adding members to cell groups...');
    await prisma.member.update({
      where: { id: members[0].id },
      data: { cell_group_id: cellGroups[0].id }
    });
    await prisma.member.update({
      where: { id: members[1].id },
      data: { cell_group_id: cellGroups[0].id }
    });
    await prisma.member.update({
      where: { id: members[2].id },
      data: { cell_group_id: cellGroups[1].id }
    });
    console.log('✅ Members assigned to cell groups');

    // 6. Create Ministries
    console.log('⛪ Creating ministries...');
    const ministryId1 = crypto.randomUUID();
    const ministryId2 = crypto.randomUUID();
    
    const ministries = await Promise.all([
      prisma.ministry.upsert({
        where: { id: ministryId1 },
        update: {},
        create: {
          id: ministryId1,
          name: 'Worship Ministry',
          description: 'Pelayanan musik dan worship',
          leader_id: members[1].id,
          status: 'active'
        }
      }),
      prisma.ministry.upsert({
        where: { id: ministryId2 },
        update: {},
        create: {
          id: ministryId2,
          name: 'Youth Ministry',
          description: 'Pelayanan anak muda',
          leader_id: members[2].id,
          status: 'active'
        }
      })
    ]);
    console.log(`✅ Created ${ministries.length} ministries`);

    // 7. Create Classes
    console.log('📚 Creating classes...');
    
    let class1 = await prisma.class.findFirst({
      where: { name: 'Basic Discipleship' }
    });
    if (!class1) {
      class1 = await prisma.class.create({
        data: {
          name: 'Basic Discipleship',
          description: 'Kelas dasar untuk murid baru',
          category: 'discipleship',
          has_levels: false,
          status: 'active',
          instructor: 'Pastor John',
          max_participants: 20
        }
      });
    }
    
    let class2 = await prisma.class.findFirst({
      where: { name: 'Leadership Development' }
    });
    if (!class2) {
      class2 = await prisma.class.create({
        data: {
          name: 'Leadership Development',
          description: 'Kelas pengembangan kepemimpinan dengan multiple levels',
          category: 'leadership',
          has_levels: true,
          status: 'active',
          instructor: 'Pastor Jane',
          max_participants: 15
        }
      });
    }
    
    const classes = [class1, class2];
    console.log(`✅ Created/found ${classes.length} classes`);

    // 8. Create class sessions for single-level class
    console.log('📖 Creating class sessions...');
    const sessions = await Promise.all([
      prisma.classSession.create({
        data: {
          class_id: classes[0].id,
          title: 'Introduction to Faith',
          description: 'Understanding basic Christian faith',
          order_number: 1,
          session_date: new Date('2024-09-10'),
          start_time: '19:00',
          end_time: '21:00'
        }
      }),
      prisma.classSession.create({
        data: {
          class_id: classes[0].id,
          title: 'Prayer and Bible Study',
          description: 'Learning to pray and study the Bible',
          order_number: 2,
          session_date: new Date('2024-09-17'),
          start_time: '19:00',
          end_time: '21:00'
        }
      })
    ]);
    console.log(`✅ Created ${sessions.length} class sessions`);

    // 9. Create class levels and topics for multi-level class
    console.log('📊 Creating class levels...');
    const level1 = await prisma.classLevel.create({
      data: {
        class_id: classes[1].id,
        name: 'Basic Leadership',
        description: 'Fundamental leadership principles',
        order_number: 1
      }
    });

    const level2 = await prisma.classLevel.create({
      data: {
        class_id: classes[1].id,
        name: 'Advanced Leadership',
        description: 'Advanced leadership techniques',
        order_number: 2
      }
    });

    // Create topics for levels
    await Promise.all([
      prisma.classTopic.create({
        data: {
          level_id: level1.id,
          name: 'Leadership Basics',
          description: 'Introduction to leadership',
          order_number: 1,
          duration_minutes: 90
        }
      }),
      prisma.classTopic.create({
        data: {
          level_id: level1.id,
          name: 'Team Building',
          description: 'Building effective teams',
          order_number: 2,
          duration_minutes: 90
        }
      }),
      prisma.classTopic.create({
        data: {
          level_id: level2.id,
          name: 'Strategic Planning',
          description: 'Planning for ministry success',
          order_number: 1,
          duration_minutes: 120
        }
      })
    ]);
    console.log('✅ Created class levels and topics');

    // 10. Create sample articles
    console.log('📰 Creating sample articles...');
    const articleId1 = crypto.randomUUID();
    const articleId2 = crypto.randomUUID();
    
    await Promise.all([
      prisma.article.upsert({
        where: { id: articleId1 },
        update: {},
        create: {
          id: articleId1,
          title: 'Welcome to Our Church',
          summary: 'Introduction to our church community',
          content: 'This is a welcome article for new members...',
          category: 'announcement',
          status: 'published',
          featured: true,
          author_id: adminUser.id
        }
      }),
      prisma.article.upsert({
        where: { id: articleId2 },
        update: {},
        create: {
          id: articleId2,
          title: 'Growing in Faith',
          summary: 'Tips for spiritual growth',
          content: 'Here are some practical tips for growing in your faith...',
          category: 'teaching',
          status: 'published',
          featured: false,
          author_id: adminUser.id
        }
      })
    ]);
    console.log('✅ Created sample articles');

    console.log('🎉 Database seeding completed successfully!');
    console.log('');
    console.log('📧 Admin Login: <EMAIL>');
    console.log('🔑 Admin Password: admin123');
    console.log('');
    console.log('👥 Sample Member Logins:');
    console.log('   <EMAIL> / member123');
    console.log('   <EMAIL> / member123');
    console.log('   <EMAIL> / member123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedDatabase();
