const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function resetPassword() {
  try {
    const correctPassword = '05102012';
    const newHash = await bcrypt.hash(correctPassword, 10);
    
    console.log('Resetting <NAME_EMAIL>');
    console.log('New hash:', newHash);
    
    const updatedUser = await prisma.member.update({
      where: { email: '<EMAIL>' },
      data: { 
        password_hash: newHash,
        password_reset_required: false 
      },
      select: {
        email: true,
        first_name: true,
        last_name: true
      }
    });
    
    console.log('Password updated for:', updatedUser.first_name, updatedUser.last_name);
    
    // Test the new password
    const testUser = await prisma.member.findUnique({
      where: { email: '<EMAIL>' },
      select: { password_hash: true }
    });
    
    const isValid = await bcrypt.compare(correctPassword, testUser.password_hash);
    console.log('Password test after update:', isValid);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetPassword();
