const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function resetAdminPassword() {
  try {
    console.log('🔐 Resetting admin password...');
    
    // Hash the new password
    const newPassword = 'admin123'; // You can change this to whatever you want
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Update the admin user
    const updatedUser = await prisma.member.update({
      where: {
        email: '<EMAIL>'
      },
      data: {
        password_hash: hashedPassword,
        password_reset_required: false
      }
    });
    
    console.log('✅ Admin password reset successfully!');
    console.log('📧 Email:', updatedUser.email);
    console.log('🔑 New password:', newPassword);
    console.log('👤 Role:', updatedUser.role, 'Level:', updatedUser.role_level);
    
  } catch (error) {
    console.error('❌ Error resetting password:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

resetAdminPassword();
