const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function resetMemberPassword() {
  try {
    console.log('🔐 Resetting member password...');
    
    // Get member data first
    const member = await prisma.member.findUnique({
      where: {
        email: '<EMAIL>'
      }
    });
    
    if (!member) {
      console.log('❌ Member not found');
      return;
    }
    
    // Generate password based on date of birth (DDMMYYYY format)
    let defaultPassword = 'Welcome123'; // fallback
    if (member.date_of_birth) {
      const date = new Date(member.date_of_birth);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear().toString();
      defaultPassword = day + month + year;
    }
    
    console.log('📅 Date of birth:', member.date_of_birth);
    console.log('🔑 Generated password:', defaultPassword);
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(defaultPassword, 12);
    
    // Update the member
    const updatedUser = await prisma.member.update({
      where: {
        email: '<EMAIL>'
      },
      data: {
        password_hash: hashedPassword,
        password_reset_required: false
      }
    });
    
    console.log('✅ Member password reset successfully!');
    console.log('📧 Email:', updatedUser.email);
    console.log('👤 Name:', updatedUser.first_name, updatedUser.last_name);
    
  } catch (error) {
    console.error('❌ Error resetting password:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

resetMemberPassword();
