const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createDummyAttendance() {
  try {
    console.log('🎯 Creating dummy attendance data...');

    // <PERSON> (our test member) and Amin cell group
    const member = await prisma.member.findUnique({
      where: { email: '<EMAIL>' },
    });

    const cellGroup = await prisma.cellGroup.findUnique({
      where: { id: '4b15f24c-6212-4f96-b98c-e53574084b10' },
    });

    if (!member || !cellGroup) {
      console.log('❌ Member or cell group not found');
      return;
    }

    // Create meetings for the last 3 months
    const meetings = [];
    const today = new Date();
    
    // Create 12 meetings (weekly meetings for 3 months)
    for (let i = 0; i < 12; i++) {
      const meetingDate = new Date(today);
      meetingDate.setDate(today.getDate() - (i * 7)); // Weekly meetings

      const meeting = await prisma.attendanceMeeting.create({
        data: {
          cell_group_id: cellGroup.id,
          meeting_date: meetingDate,
          meeting_type: 'regular',
          topic: `Cell Group Meeting ${12 - i}`,
          location: 'Rumah Darrell',
          event_category: 'cell_group',
          created_by: member.id,
          notes: `Regular weekly cell group meeting`,
        },
      });

      meetings.push(meeting);
      console.log(`📅 Created meeting: ${meeting.topic} on ${meetingDate.toISOString().split('T')[0]}`);
    }

    // Create attendance for Darrell with varied statuses
    const attendanceStatuses = ['present', 'present', 'present', 'late', 'present', 'absent', 'present', 'present', 'present', 'late', 'present', 'present'];
    
    for (let i = 0; i < meetings.length; i++) {
      const meeting = meetings[i];
      const status = attendanceStatuses[i];

      await prisma.attendanceParticipant.create({
        data: {
          meeting_id: meeting.id,
          member_id: member.id,
          status: status,
          notes: status === 'absent' ? 'Sakit' : status === 'late' ? 'Terlambat karena macet' : null,
        },
      });

      console.log(`👤 Created attendance: ${member.first_name} - ${status} for ${meeting.topic}`);
    }

    // Create attendance for other cell group members
    const otherMembers = await prisma.cellGroupMember.findMany({
      where: {
        cell_group_id: cellGroup.id,
        member_id: { not: member.id },
        status: 'active',
      },
      include: { member: true },
      take: 5, // Get up to 5 other members
    });

    for (const cgMember of otherMembers) {
      for (let i = 0; i < meetings.length; i++) {
        const meeting = meetings[i];
        // Random attendance pattern for other members
        const randomStatus = Math.random() > 0.8 ? 'absent' : Math.random() > 0.9 ? 'late' : 'present';

        await prisma.attendanceParticipant.create({
          data: {
            meeting_id: meeting.id,
            member_id: cgMember.member.id,
            status: randomStatus,
            notes: randomStatus === 'absent' ? 'Berhalangan hadir' : null,
          },
        });

        console.log(`👥 Created attendance: ${cgMember.member.first_name} - ${randomStatus} for ${meeting.topic}`);
      }
    }

    console.log('✅ Dummy attendance data created successfully!');
    
    // Show statistics
    const totalMeetings = meetings.length;
    const darrellAttendance = await prisma.attendanceParticipant.count({
      where: {
        member_id: member.id,
        status: 'present',
      },
    });
    
    console.log(`📊 Statistics:`);
    console.log(`   Total meetings: ${totalMeetings}`);
    console.log(`   Darrell present: ${darrellAttendance}/${totalMeetings} (${Math.round((darrellAttendance / totalMeetings) * 100)}%)`);

  } catch (error) {
    console.error('❌ Error creating dummy attendance:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDummyAttendance();
