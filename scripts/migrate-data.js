#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const { createClient } = require('@supabase/supabase-js');

// Configuration
const prisma = new PrismaClient();

// Command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
  console.log('Usage: node migrate-data.js <SUPABASE_URL> <SUPABASE_SERVICE_KEY>');
  console.log('Example: node migrate-data.js https://your-project.supabase.co your-service-key');
  process.exit(1);
}

const SUPABASE_URL = args[0];
const SUPABASE_KEY = args[1];

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Utility functions
function parseDate(dateStr) {
  if (!dateStr) return null;
  const date = new Date(dateStr);
  return isNaN(date.getTime()) ? null : date;
}

function safeJsonParse(str) {
  try {
    return JSON.parse(str);
  } catch (e) {
    return null;
  }
}

class DataMigrator {
  constructor() {
    this.stats = {
      districts: { total: 0, migrated: 0, errors: 0 },
      members: { total: 0, migrated: 0, errors: 0 },
      cellGroups: { total: 0, migrated: 0, errors: 0 },
      cellGroupMembers: { total: 0, migrated: 0, errors: 0 },
      attendanceMeetings: { total: 0, migrated: 0, errors: 0 },
      attendanceParticipants: { total: 0, migrated: 0, errors: 0 },
      attendanceVisitors: { total: 0, migrated: 0, errors: 0 }
    };
  }

  async testConnections() {
    console.log('🔍 Testing connections...');
    
    try {
      // Test Supabase connection
      const { data, error } = await supabase.from('members').select('count').limit(1);
      if (error) throw new Error(`Supabase: ${error.message}`);
      console.log('✅ Supabase connection successful');

      // Test MySQL connection
      await prisma.$connect();
      console.log('✅ MySQL connection successful\n');
    } catch (error) {
      console.error('❌ Connection test failed:', error.message);
      process.exit(1);
    }
  }

  async migrateDistricts() {
    console.log('📍 Migrating Districts...');
    
    try {
      const { data: districts, error } = await supabase.from('districts').select('*');
      
      if (error) {
        console.error('❌ Error fetching districts:', error.message);
        return;
      }

      this.stats.districts.total = districts.length;
      console.log(`Found ${districts.length} districts`);

      for (const district of districts) {
        try {
          await prisma.district.upsert({
            where: { id: district.id },
            update: {
              name: district.name,
              description: district.description,
              leader1_id: district.leader1_id,
              leader2_id: district.leader2_id,
              status: district.status || 'active',
              created_at: parseDate(district.created_at) || new Date(),
              updated_at: parseDate(district.updated_at) || new Date()
            },
            create: {
              id: district.id,
              name: district.name,
              description: district.description,
              leader1_id: district.leader1_id,
              leader2_id: district.leader2_id,
              status: district.status || 'active',
              created_at: parseDate(district.created_at) || new Date(),
              updated_at: parseDate(district.updated_at) || new Date()
            }
          });

          this.stats.districts.migrated++;
        } catch (error) {
          this.stats.districts.errors++;
          console.error(`❌ Error migrating district ${district.id}:`, error.message);
        }
      }

      console.log(`✅ Districts: ${this.stats.districts.migrated}/${this.stats.districts.total} migrated (${this.stats.districts.errors} errors)\n`);
    } catch (error) {
      console.error('❌ Fatal error in districts migration:', error.message);
    }
  }

  async migrateMembers() {
    console.log('👥 Migrating Members...');
    
    try {
      const { data: members, error } = await supabase.from('members').select('*');
      
      if (error) {
        console.error('❌ Error fetching members:', error.message);
        return;
      }

      this.stats.members.total = members.length;
      console.log(`Found ${members.length} members`);

      for (const member of members) {
        try {
          await prisma.member.upsert({
            where: { id: member.id },
            update: {
              email: member.email,
              first_name: member.first_name,
              last_name: member.last_name,
              phone: member.phone,
              address: member.address,
              date_of_birth: parseDate(member.date_of_birth),
              gender: member.gender,
              marital_status: member.marital_status,
              occupation: member.occupation,
              join_date: parseDate(member.join_date) || new Date(),
              emergency_contact_name: member.emergency_contact_name,
              emergency_contact_phone: member.emergency_contact_phone,
              notes: member.notes,
              status: member.status || 'active',
              role: member.role || 'member',
              role_level: member.role_level || 1,
              role_context: safeJsonParse(member.role_context),
              password_hash: member.password_hash,
              password_reset_required: member.password_reset_required || false,
              cell_group_id: member.cell_group_id,
              district_id: member.district_id,
              created_at: parseDate(member.created_at) || new Date(),
              updated_at: parseDate(member.updated_at) || new Date()
            },
            create: {
              id: member.id,
              email: member.email,
              first_name: member.first_name,
              last_name: member.last_name,
              phone: member.phone,
              address: member.address,
              date_of_birth: parseDate(member.date_of_birth),
              gender: member.gender,
              marital_status: member.marital_status,
              occupation: member.occupation,
              join_date: parseDate(member.join_date) || new Date(),
              emergency_contact_name: member.emergency_contact_name,
              emergency_contact_phone: member.emergency_contact_phone,
              notes: member.notes,
              status: member.status || 'active',
              role: member.role || 'member',
              role_level: member.role_level || 1,
              role_context: safeJsonParse(member.role_context),
              password_hash: member.password_hash,
              password_reset_required: member.password_reset_required || false,
              cell_group_id: member.cell_group_id,
              district_id: member.district_id,
              created_at: parseDate(member.created_at) || new Date(),
              updated_at: parseDate(member.updated_at) || new Date()
            }
          });

          this.stats.members.migrated++;
        } catch (error) {
          this.stats.members.errors++;
          console.error(`❌ Error migrating member ${member.email}:`, error.message);
        }
      }

      console.log(`✅ Members: ${this.stats.members.migrated}/${this.stats.members.total} migrated (${this.stats.members.errors} errors)\n`);
    } catch (error) {
      console.error('❌ Fatal error in members migration:', error.message);
    }
  }

  async migrateCellGroups() {
    console.log('🏠 Migrating Cell Groups...');
    
    try {
      const { data: cellGroups, error } = await supabase.from('cell_groups').select('*');
      
      if (error) {
        console.error('❌ Error fetching cell groups:', error.message);
        return;
      }

      this.stats.cellGroups.total = cellGroups.length;
      console.log(`Found ${cellGroups.length} cell groups`);

      for (const cellGroup of cellGroups) {
        try {
          await prisma.cellGroup.upsert({
            where: { id: cellGroup.id },
            update: {
              name: cellGroup.name,
              description: cellGroup.description,
              district_id: cellGroup.district_id,
              leader_id: cellGroup.leader_id,
              assistant_leader_id: cellGroup.assistant_leader_id,
              meeting_day: cellGroup.meeting_day,
              meeting_time: cellGroup.meeting_time,
              meeting_location: cellGroup.meeting_location,
              status: cellGroup.status || 'active',
              created_at: parseDate(cellGroup.created_at) || new Date(),
              updated_at: parseDate(cellGroup.updated_at) || new Date()
            },
            create: {
              id: cellGroup.id,
              name: cellGroup.name,
              description: cellGroup.description,
              district_id: cellGroup.district_id,
              leader_id: cellGroup.leader_id,
              assistant_leader_id: cellGroup.assistant_leader_id,
              meeting_day: cellGroup.meeting_day,
              meeting_time: cellGroup.meeting_time,
              meeting_location: cellGroup.meeting_location,
              status: cellGroup.status || 'active',
              created_at: parseDate(cellGroup.created_at) || new Date(),
              updated_at: parseDate(cellGroup.updated_at) || new Date()
            }
          });

          this.stats.cellGroups.migrated++;
        } catch (error) {
          this.stats.cellGroups.errors++;
          console.error(`❌ Error migrating cell group ${cellGroup.name}:`, error.message);
        }
      }

      console.log(`✅ Cell Groups: ${this.stats.cellGroups.migrated}/${this.stats.cellGroups.total} migrated (${this.stats.cellGroups.errors} errors)\n`);
    } catch (error) {
      console.error('❌ Fatal error in cell groups migration:', error.message);
    }
  }

  async migrateCellGroupMembers() {
    console.log('👥 Migrating Cell Group Members...');
    
    try {
      const { data: cgMembers, error } = await supabase.from('cell_group_members').select('*');
      
      if (error) {
        console.error('❌ Error fetching cell group members:', error.message);
        return;
      }

      this.stats.cellGroupMembers.total = cgMembers.length;
      console.log(`Found ${cgMembers.length} cell group memberships`);

      for (const cgMember of cgMembers) {
        try {
          await prisma.cellGroupMember.upsert({
            where: { id: cgMember.id },
            update: {
              cell_group_id: cgMember.cell_group_id,
              member_id: cgMember.member_id,
              joined_date: parseDate(cgMember.joined_date) || new Date(),
              status: cgMember.status || 'active',
              created_at: parseDate(cgMember.created_at) || new Date(),
              updated_at: parseDate(cgMember.updated_at) || new Date()
            },
            create: {
              id: cgMember.id,
              cell_group_id: cgMember.cell_group_id,
              member_id: cgMember.member_id,
              joined_date: parseDate(cgMember.joined_date) || new Date(),
              status: cgMember.status || 'active',
              created_at: parseDate(cgMember.created_at) || new Date(),
              updated_at: parseDate(cgMember.updated_at) || new Date()
            }
          });

          this.stats.cellGroupMembers.migrated++;
        } catch (error) {
          this.stats.cellGroupMembers.errors++;
          console.error(`❌ Error migrating cell group member:`, error.message);
        }
      }

      console.log(`✅ Cell Group Members: ${this.stats.cellGroupMembers.migrated}/${this.stats.cellGroupMembers.total} migrated (${this.stats.cellGroupMembers.errors} errors)\n`);
    } catch (error) {
      console.error('❌ Fatal error in cell group members migration:', error.message);
    }
  }

  async migrateAttendance() {
    console.log('📊 Migrating Attendance Data...');
    
    // Migrate Attendance Meetings
    try {
      const { data: meetings, error } = await supabase.from('attendance_meetings').select('*');
      
      if (error) {
        console.error('❌ Error fetching attendance meetings:', error.message);
        return;
      }

      this.stats.attendanceMeetings.total = meetings.length;
      console.log(`Found ${meetings.length} attendance meetings`);

      for (const meeting of meetings) {
        try {
          await prisma.attendanceMeeting.upsert({
            where: { id: meeting.id },
            update: {
              cell_group_id: meeting.cell_group_id,
              meeting_date: parseDate(meeting.meeting_date) || new Date(),
              meeting_type: meeting.meeting_type || 'regular',
              topic: meeting.topic,
              notes: meeting.notes,
              location: meeting.location,
              offering: meeting.offering ? parseFloat(meeting.offering) : null,
              created_by: meeting.created_by,
              ministry_id: meeting.ministry_id,
              event_category: meeting.event_category,
              is_realtime: meeting.is_realtime || false,
              live_checkin_active: meeting.live_checkin_active || false,
              live_checkin_expires_at: parseDate(meeting.live_checkin_expires_at),
              qr_code_data: meeting.qr_code_data,
              created_at: parseDate(meeting.created_at) || new Date(),
              updated_at: parseDate(meeting.updated_at) || new Date()
            },
            create: {
              id: meeting.id,
              cell_group_id: meeting.cell_group_id,
              meeting_date: parseDate(meeting.meeting_date) || new Date(),
              meeting_type: meeting.meeting_type || 'regular',
              topic: meeting.topic,
              notes: meeting.notes,
              location: meeting.location,
              offering: meeting.offering ? parseFloat(meeting.offering) : null,
              created_by: meeting.created_by,
              ministry_id: meeting.ministry_id,
              event_category: meeting.event_category,
              is_realtime: meeting.is_realtime || false,
              live_checkin_active: meeting.live_checkin_active || false,
              live_checkin_expires_at: parseDate(meeting.live_checkin_expires_at),
              qr_code_data: meeting.qr_code_data,
              created_at: parseDate(meeting.created_at) || new Date(),
              updated_at: parseDate(meeting.updated_at) || new Date()
            }
          });

          this.stats.attendanceMeetings.migrated++;
        } catch (error) {
          this.stats.attendanceMeetings.errors++;
          console.error(`❌ Error migrating attendance meeting:`, error.message);
        }
      }

      console.log(`✅ Attendance Meetings: ${this.stats.attendanceMeetings.migrated}/${this.stats.attendanceMeetings.total} migrated (${this.stats.attendanceMeetings.errors} errors)`);
    } catch (error) {
      console.error('❌ Fatal error in attendance meetings migration:', error.message);
    }

    // Migrate Attendance Participants
    try {
      const { data: participants, error } = await supabase.from('attendance_participants').select('*');
      
      if (error) {
        console.error('❌ Error fetching attendance participants:', error.message);
        return;
      }

      this.stats.attendanceParticipants.total = participants.length;
      console.log(`Found ${participants.length} attendance participants`);

      for (const participant of participants) {
        try {
          await prisma.attendanceParticipant.upsert({
            where: { id: participant.id },
            update: {
              meeting_id: participant.meeting_id,
              member_id: participant.member_id,
              status: participant.status || 'present',
              notes: participant.notes,
              created_at: parseDate(participant.created_at) || new Date(),
              updated_at: parseDate(participant.updated_at) || new Date()
            },
            create: {
              id: participant.id,
              meeting_id: participant.meeting_id,
              member_id: participant.member_id,
              status: participant.status || 'present',
              notes: participant.notes,
              created_at: parseDate(participant.created_at) || new Date(),
              updated_at: parseDate(participant.updated_at) || new Date()
            }
          });

          this.stats.attendanceParticipants.migrated++;
        } catch (error) {
          this.stats.attendanceParticipants.errors++;
          console.error(`❌ Error migrating attendance participant:`, error.message);
        }
      }

      console.log(`✅ Attendance Participants: ${this.stats.attendanceParticipants.migrated}/${this.stats.attendanceParticipants.total} migrated (${this.stats.attendanceParticipants.errors} errors)`);
    } catch (error) {
      console.error('❌ Fatal error in attendance participants migration:', error.message);
    }

    // Migrate Attendance Visitors
    try {
      const { data: visitors, error } = await supabase.from('attendance_visitors').select('*');
      
      if (error) {
        console.error('❌ Error fetching attendance visitors:', error.message);
        return;
      }

      this.stats.attendanceVisitors.total = visitors.length;
      console.log(`Found ${visitors.length} attendance visitors`);

      for (const visitor of visitors) {
        try {
          await prisma.attendanceVisitor.upsert({
            where: { id: visitor.id },
            update: {
              meeting_id: visitor.meeting_id,
              first_name: visitor.first_name,
              last_name: visitor.last_name,
              phone: visitor.phone,
              email: visitor.email,
              address: visitor.address,
              notes: visitor.notes,
              converted_to_member_id: visitor.converted_to_member_id,
              created_at: parseDate(visitor.created_at) || new Date()
            },
            create: {
              id: visitor.id,
              meeting_id: visitor.meeting_id,
              first_name: visitor.first_name,
              last_name: visitor.last_name,
              phone: visitor.phone,
              email: visitor.email,
              address: visitor.address,
              notes: visitor.notes,
              converted_to_member_id: visitor.converted_to_member_id,
              created_at: parseDate(visitor.created_at) || new Date()
            }
          });

          this.stats.attendanceVisitors.migrated++;
        } catch (error) {
          this.stats.attendanceVisitors.errors++;
          console.error(`❌ Error migrating attendance visitor:`, error.message);
        }
      }

      console.log(`✅ Attendance Visitors: ${this.stats.attendanceVisitors.migrated}/${this.stats.attendanceVisitors.total} migrated (${this.stats.attendanceVisitors.errors} errors)\n`);
    } catch (error) {
      console.error('❌ Fatal error in attendance visitors migration:', error.message);
    }
  }

  async run() {
    try {
      console.log('🚀 Church Management Data Migration Tool');
      console.log('==========================================\n');

      await this.testConnections();

      console.log('🔄 Starting migration process...\n');

      // Migration order is important due to foreign key constraints
      await this.migrateDistricts();
      await this.migrateMembers();
      await this.migrateCellGroups();
      await this.migrateCellGroupMembers();
      await this.migrateAttendance();

      console.log('📊 Migration Summary:');
      console.log('====================');
      console.log(`Districts: ${this.stats.districts.migrated}/${this.stats.districts.total} (${this.stats.districts.errors} errors)`);
      console.log(`Members: ${this.stats.members.migrated}/${this.stats.members.total} (${this.stats.members.errors} errors)`);
      console.log(`Cell Groups: ${this.stats.cellGroups.migrated}/${this.stats.cellGroups.total} (${this.stats.cellGroups.errors} errors)`);
      console.log(`Cell Group Members: ${this.stats.cellGroupMembers.migrated}/${this.stats.cellGroupMembers.total} (${this.stats.cellGroupMembers.errors} errors)`);
      console.log(`Attendance Meetings: ${this.stats.attendanceMeetings.migrated}/${this.stats.attendanceMeetings.total} (${this.stats.attendanceMeetings.errors} errors)`);
      console.log(`Attendance Participants: ${this.stats.attendanceParticipants.migrated}/${this.stats.attendanceParticipants.total} (${this.stats.attendanceParticipants.errors} errors)`);
      console.log(`Attendance Visitors: ${this.stats.attendanceVisitors.migrated}/${this.stats.attendanceVisitors.total} (${this.stats.attendanceVisitors.errors} errors)`);

      const totalMigrated = this.stats.districts.migrated + this.stats.members.migrated + 
                           this.stats.cellGroups.migrated + this.stats.cellGroupMembers.migrated +
                           this.stats.attendanceMeetings.migrated + this.stats.attendanceParticipants.migrated +
                           this.stats.attendanceVisitors.migrated;

      console.log(`\n🎉 Migration completed! Total records migrated: ${totalMigrated}`);

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    } finally {
      await prisma.$disconnect();
    }
  }
}

// Run the migration
const migrator = new DataMigrator();
migrator.run().catch(console.error);
