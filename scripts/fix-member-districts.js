const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixMemberDistricts() {
  console.log('🚀 Starting member district assignment...');
  
  try {
    // Get all members who are in cell groups but don't have district_id assigned
    const membersInCellGroups = await prisma.cellGroupMember.findMany({
      select: {
        member_id: true,
        cell_group: {
          select: {
            id: true,
            name: true,
            district_id: true,
          }
        }
      }
    });

    console.log(`📊 Found ${membersInCellGroups.length} cell group memberships`);

    let updatedCount = 0;
    
    for (const cgMember of membersInCellGroups) {
      if (cgMember.cell_group.district_id) {
        try {
          // Update member's district_id and cell_group_id based on their cell group membership
          await prisma.member.update({
            where: { id: cgMember.member_id },
            data: {
              district_id: cgMember.cell_group.district_id,
              cell_group_id: cgMember.cell_group.id,
            }
          });
          
          updatedCount++;
          
          if (updatedCount % 20 === 0) {
            console.log(`📈 Updated ${updatedCount} members so far...`);
          }
        } catch (error) {
          console.log(`⚠️ Failed to update member ${cgMember.member_id}: ${error.message}`);
        }
      }
    }

    console.log(`✅ Updated ${updatedCount} member district assignments`);

    // Verify results
    console.log('\n🔍 Verifying results...');
    
    const districts = await prisma.district.findMany({
      where: { status: 'active' },
      select: {
        id: true,
        name: true,
        _count: {
          select: { 
            cell_groups: { where: { status: 'active' } },
            members: { where: { status: 'active' } },
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    console.log('\n📊 District Statistics:');
    districts.forEach(district => {
      console.log(`• ${district.name}: ${district._count.members} members, ${district._count.cell_groups} cell groups`);
    });

  } catch (error) {
    console.error('❌ Error fixing member districts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixMemberDistricts();
