const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testMemberAttendance() {
  try {
    console.log('🔍 Testing member attendance data...');
    
    // Find Darrell
    const darrell = await prisma.member.findUnique({
      where: { email: '<EMAIL>' },
      select: { id: true, first_name: true, last_name: true }
    });
    
    if (!darrell) {
      console.log('❌ Darrell not found');
      return;
    }
    
    console.log('Found member:', darrell.first_name, darrell.last_name);
    
    // Check his attendance records directly from database
    const attendanceCount = await prisma.attendanceParticipant.count({
      where: { member_id: darrell.id }
    });
    
    console.log('📊 Total attendance records for Darrell:', attendanceCount);
    
    if (attendanceCount > 0) {
      // Get sample records
      const sampleRecords = await prisma.attendanceParticipant.findMany({
        where: { member_id: darrell.id },
        take: 5,
        orderBy: { 
          meeting: { meeting_date: 'desc' } 
        },
        include: {
          meeting: {
            select: {
              meeting_date: true,
              topic: true,
              event_category: true,
              cell_group: { select: { name: true } }
            }
          }
        }
      });
      
      console.log('Recent attendance records:');
      sampleRecords.forEach(record => {
        const meetingContext = record.meeting.cell_group?.name || record.meeting.event_category;
        console.log(`  - ${record.meeting.meeting_date.toISOString().split('T')[0]}: ${record.status} at ${record.meeting.topic || 'Meeting'} [${meetingContext}]`);
      });
      
      // Test statistics calculation
      const allRecords = await prisma.attendanceParticipant.findMany({
        where: { member_id: darrell.id },
        select: { status: true }
      });
      
      const stats = {
        total: allRecords.length,
        present: allRecords.filter(r => r.status === 'present').length,
        absent: allRecords.filter(r => r.status === 'absent').length,
        late: allRecords.filter(r => r.status === 'late').length
      };
      
      stats.percentage = stats.total > 0 ? Math.round((stats.present / stats.total) * 100) : 0;
      
      console.log('📈 Statistics:', stats);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testMemberAttendance();
