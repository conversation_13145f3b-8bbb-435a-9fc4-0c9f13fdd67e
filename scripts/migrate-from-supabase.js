#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Configuration
const prisma = new PrismaClient();

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Utility function to ask questions
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Utility function to safely parse JSON
function safeJsonParse(str) {
  try {
    return JSON.parse(str);
  } catch (e) {
    return null;
  }
}

// Utility function to convert date strings to Date objects
function parseDate(dateStr) {
  if (!dateStr) return null;
  const date = new Date(dateStr);
  return isNaN(date.getTime()) ? null : date;
}

class SupabaseMigrator {
  constructor() {
    this.supabase = null;
    this.stats = {
      members: { total: 0, migrated: 0, errors: 0 },
      districts: { total: 0, migrated: 0, errors: 0 },
      cellGroups: { total: 0, migrated: 0, errors: 0 },
      attendance: { total: 0, migrated: 0, errors: 0 }
    };
  }

  async initialize() {
    console.log('🚀 Church Management Data Migration Tool');
    console.log('==========================================\n');

    // Get Supabase credentials
    const supabaseUrl = await askQuestion('Enter your Supabase URL: ');
    const supabaseKey = await askQuestion('Enter your Supabase Service Role Key: ');

    this.supabase = createClient(supabaseUrl, supabaseKey);

    console.log('\n✅ Supabase connection initialized');
    
    // Test connection
    try {
      const { data, error } = await this.supabase.from('members').select('count').limit(1);
      if (error) {
        console.error('❌ Failed to connect to Supabase:', error.message);
        process.exit(1);
      }
      console.log('✅ Supabase connection test successful');
    } catch (error) {
      console.error('❌ Failed to test Supabase connection:', error.message);
      process.exit(1);
    }

    // Test MySQL connection
    try {
      await prisma.$connect();
      console.log('✅ MySQL connection test successful\n');
    } catch (error) {
      console.error('❌ Failed to connect to MySQL:', error.message);
      process.exit(1);
    }
  }

  async migrateDistricts() {
    console.log('📍 Migrating Districts...');
    
    try {
      // Fetch districts from Supabase
      const { data: districts, error } = await this.supabase
        .from('districts')
        .select('*');

      if (error) {
        console.error('❌ Error fetching districts from Supabase:', error.message);
        return;
      }

      this.stats.districts.total = districts.length;
      console.log(`Found ${districts.length} districts to migrate`);

      for (const district of districts) {
        try {
          await prisma.district.upsert({
            where: { id: district.id },
            update: {
              name: district.name,
              description: district.description,
              leader1_id: district.leader1_id,
              leader2_id: district.leader2_id,
              status: district.status || 'active',
              created_at: parseDate(district.created_at) || new Date(),
              updated_at: parseDate(district.updated_at) || new Date()
            },
            create: {
              id: district.id,
              name: district.name,
              description: district.description,
              leader1_id: district.leader1_id,
              leader2_id: district.leader2_id,
              status: district.status || 'active',
              created_at: parseDate(district.created_at) || new Date(),
              updated_at: parseDate(district.updated_at) || new Date()
            }
          });

          this.stats.districts.migrated++;
          process.stdout.write(`\r✅ Migrated ${this.stats.districts.migrated}/${this.stats.districts.total} districts`);
        } catch (error) {
          this.stats.districts.errors++;
          console.error(`\n❌ Error migrating district ${district.id}:`, error.message);
        }
      }

      console.log(`\n✅ Districts migration completed: ${this.stats.districts.migrated} migrated, ${this.stats.districts.errors} errors\n`);
    } catch (error) {
      console.error('❌ Fatal error in districts migration:', error.message);
    }
  }

  async migrateMembers() {
    console.log('👥 Migrating Members...');
    
    try {
      // Fetch members from Supabase
      const { data: members, error } = await this.supabase
        .from('members')
        .select('*');

      if (error) {
        console.error('❌ Error fetching members from Supabase:', error.message);
        return;
      }

      this.stats.members.total = members.length;
      console.log(`Found ${members.length} members to migrate`);

      for (const member of members) {
        try {
          await prisma.member.upsert({
            where: { id: member.id },
            update: {
              email: member.email,
              first_name: member.first_name,
              last_name: member.last_name,
              phone: member.phone,
              address: member.address,
              date_of_birth: parseDate(member.date_of_birth),
              gender: member.gender,
              marital_status: member.marital_status,
              occupation: member.occupation,
              join_date: parseDate(member.join_date) || new Date(),
              emergency_contact_name: member.emergency_contact_name,
              emergency_contact_phone: member.emergency_contact_phone,
              notes: member.notes,
              status: member.status || 'active',
              role: member.role || 'member',
              role_level: member.role_level || 1,
              role_context: safeJsonParse(member.role_context),
              password_hash: member.password_hash,
              password_reset_required: member.password_reset_required || false,
              cell_group_id: member.cell_group_id,
              district_id: member.district_id,
              created_at: parseDate(member.created_at) || new Date(),
              updated_at: parseDate(member.updated_at) || new Date()
            },
            create: {
              id: member.id,
              email: member.email,
              first_name: member.first_name,
              last_name: member.last_name,
              phone: member.phone,
              address: member.address,
              date_of_birth: parseDate(member.date_of_birth),
              gender: member.gender,
              marital_status: member.marital_status,
              occupation: member.occupation,
              join_date: parseDate(member.join_date) || new Date(),
              emergency_contact_name: member.emergency_contact_name,
              emergency_contact_phone: member.emergency_contact_phone,
              notes: member.notes,
              status: member.status || 'active',
              role: member.role || 'member',
              role_level: member.role_level || 1,
              role_context: safeJsonParse(member.role_context),
              password_hash: member.password_hash,
              password_reset_required: member.password_reset_required || false,
              cell_group_id: member.cell_group_id,
              district_id: member.district_id,
              created_at: parseDate(member.created_at) || new Date(),
              updated_at: parseDate(member.updated_at) || new Date()
            }
          });

          this.stats.members.migrated++;
          process.stdout.write(`\r✅ Migrated ${this.stats.members.migrated}/${this.stats.members.total} members`);
        } catch (error) {
          this.stats.members.errors++;
          console.error(`\n❌ Error migrating member ${member.id}:`, error.message);
        }
      }

      console.log(`\n✅ Members migration completed: ${this.stats.members.migrated} migrated, ${this.stats.members.errors} errors\n`);
    } catch (error) {
      console.error('❌ Fatal error in members migration:', error.message);
    }
  }

  async migrateCellGroups() {
    console.log('🏠 Migrating Cell Groups...');
    
    try {
      // Fetch cell groups from Supabase
      const { data: cellGroups, error } = await this.supabase
        .from('cell_groups')
        .select('*');

      if (error) {
        console.error('❌ Error fetching cell groups from Supabase:', error.message);
        return;
      }

      this.stats.cellGroups.total = cellGroups.length;
      console.log(`Found ${cellGroups.length} cell groups to migrate`);

      for (const cellGroup of cellGroups) {
        try {
          await prisma.cellGroup.upsert({
            where: { id: cellGroup.id },
            update: {
              name: cellGroup.name,
              description: cellGroup.description,
              district_id: cellGroup.district_id,
              leader_id: cellGroup.leader_id,
              assistant_leader_id: cellGroup.assistant_leader_id,
              meeting_day: cellGroup.meeting_day,
              meeting_time: cellGroup.meeting_time,
              meeting_location: cellGroup.meeting_location,
              status: cellGroup.status || 'active',
              created_at: parseDate(cellGroup.created_at) || new Date(),
              updated_at: parseDate(cellGroup.updated_at) || new Date()
            },
            create: {
              id: cellGroup.id,
              name: cellGroup.name,
              description: cellGroup.description,
              district_id: cellGroup.district_id,
              leader_id: cellGroup.leader_id,
              assistant_leader_id: cellGroup.assistant_leader_id,
              meeting_day: cellGroup.meeting_day,
              meeting_time: cellGroup.meeting_time,
              meeting_location: cellGroup.meeting_location,
              status: cellGroup.status || 'active',
              created_at: parseDate(cellGroup.created_at) || new Date(),
              updated_at: parseDate(cellGroup.updated_at) || new Date()
            }
          });

          this.stats.cellGroups.migrated++;
          process.stdout.write(`\r✅ Migrated ${this.stats.cellGroups.migrated}/${this.stats.cellGroups.total} cell groups`);
        } catch (error) {
          this.stats.cellGroups.errors++;
          console.error(`\n❌ Error migrating cell group ${cellGroup.id}:`, error.message);
        }
      }

      console.log(`\n✅ Cell Groups migration completed: ${this.stats.cellGroups.migrated} migrated, ${this.stats.cellGroups.errors} errors\n`);
    } catch (error) {
      console.error('❌ Fatal error in cell groups migration:', error.message);
    }
  }

  async migrateAttendance() {
    console.log('📊 Migrating Attendance Data...');
    
    try {
      // Migrate Attendance Meetings
      const { data: meetings, error: meetingsError } = await this.supabase
        .from('attendance_meetings')
        .select('*');

      if (meetingsError) {
        console.error('❌ Error fetching attendance meetings from Supabase:', meetingsError.message);
        return;
      }

      console.log(`Found ${meetings.length} attendance meetings to migrate`);
      let migratedMeetings = 0;

      for (const meeting of meetings) {
        try {
          await prisma.attendanceMeeting.upsert({
            where: { id: meeting.id },
            update: {
              cell_group_id: meeting.cell_group_id,
              meeting_date: parseDate(meeting.meeting_date) || new Date(),
              meeting_type: meeting.meeting_type || 'regular',
              topic: meeting.topic,
              notes: meeting.notes,
              location: meeting.location,
              offering: meeting.offering ? parseFloat(meeting.offering) : null,
              created_by: meeting.created_by,
              ministry_id: meeting.ministry_id,
              event_category: meeting.event_category,
              is_realtime: meeting.is_realtime || false,
              live_checkin_active: meeting.live_checkin_active || false,
              live_checkin_expires_at: parseDate(meeting.live_checkin_expires_at),
              qr_code_data: meeting.qr_code_data,
              created_at: parseDate(meeting.created_at) || new Date(),
              updated_at: parseDate(meeting.updated_at) || new Date()
            },
            create: {
              id: meeting.id,
              cell_group_id: meeting.cell_group_id,
              meeting_date: parseDate(meeting.meeting_date) || new Date(),
              meeting_type: meeting.meeting_type || 'regular',
              topic: meeting.topic,
              notes: meeting.notes,
              location: meeting.location,
              offering: meeting.offering ? parseFloat(meeting.offering) : null,
              created_by: meeting.created_by,
              ministry_id: meeting.ministry_id,
              event_category: meeting.event_category,
              is_realtime: meeting.is_realtime || false,
              live_checkin_active: meeting.live_checkin_active || false,
              live_checkin_expires_at: parseDate(meeting.live_checkin_expires_at),
              qr_code_data: meeting.qr_code_data,
              created_at: parseDate(meeting.created_at) || new Date(),
              updated_at: parseDate(meeting.updated_at) || new Date()
            }
          });

          migratedMeetings++;
          process.stdout.write(`\r✅ Migrated ${migratedMeetings}/${meetings.length} attendance meetings`);
        } catch (error) {
          console.error(`\n❌ Error migrating attendance meeting ${meeting.id}:`, error.message);
        }
      }

      // Migrate Attendance Participants
      const { data: participants, error: participantsError } = await this.supabase
        .from('attendance_participants')
        .select('*');

      if (participantsError) {
        console.error('❌ Error fetching attendance participants from Supabase:', participantsError.message);
        return;
      }

      console.log(`\nFound ${participants.length} attendance participants to migrate`);
      let migratedParticipants = 0;

      for (const participant of participants) {
        try {
          await prisma.attendanceParticipant.upsert({
            where: { id: participant.id },
            update: {
              meeting_id: participant.meeting_id,
              member_id: participant.member_id,
              status: participant.status || 'present',
              notes: participant.notes,
              created_at: parseDate(participant.created_at) || new Date(),
              updated_at: parseDate(participant.updated_at) || new Date()
            },
            create: {
              id: participant.id,
              meeting_id: participant.meeting_id,
              member_id: participant.member_id,
              status: participant.status || 'present',
              notes: participant.notes,
              created_at: parseDate(participant.created_at) || new Date(),
              updated_at: parseDate(participant.updated_at) || new Date()
            }
          });

          migratedParticipants++;
          process.stdout.write(`\r✅ Migrated ${migratedParticipants}/${participants.length} attendance participants`);
        } catch (error) {
          console.error(`\n❌ Error migrating attendance participant ${participant.id}:`, error.message);
        }
      }

      // Migrate Attendance Visitors
      const { data: visitors, error: visitorsError } = await this.supabase
        .from('attendance_visitors')
        .select('*');

      if (visitorsError) {
        console.error('❌ Error fetching attendance visitors from Supabase:', visitorsError.message);
        return;
      }

      console.log(`\nFound ${visitors.length} attendance visitors to migrate`);
      let migratedVisitors = 0;

      for (const visitor of visitors) {
        try {
          await prisma.attendanceVisitor.upsert({
            where: { id: visitor.id },
            update: {
              meeting_id: visitor.meeting_id,
              first_name: visitor.first_name,
              last_name: visitor.last_name,
              phone: visitor.phone,
              email: visitor.email,
              address: visitor.address,
              notes: visitor.notes,
              converted_to_member_id: visitor.converted_to_member_id,
              created_at: parseDate(visitor.created_at) || new Date()
            },
            create: {
              id: visitor.id,
              meeting_id: visitor.meeting_id,
              first_name: visitor.first_name,
              last_name: visitor.last_name,
              phone: visitor.phone,
              email: visitor.email,
              address: visitor.address,
              notes: visitor.notes,
              converted_to_member_id: visitor.converted_to_member_id,
              created_at: parseDate(visitor.created_at) || new Date()
            }
          });

          migratedVisitors++;
          process.stdout.write(`\r✅ Migrated ${migratedVisitors}/${visitors.length} attendance visitors`);
        } catch (error) {
          console.error(`\n❌ Error migrating attendance visitor ${visitor.id}:`, error.message);
        }
      }

      this.stats.attendance.total = meetings.length + participants.length + visitors.length;
      this.stats.attendance.migrated = migratedMeetings + migratedParticipants + migratedVisitors;

      console.log(`\n✅ Attendance migration completed: ${this.stats.attendance.migrated} records migrated\n`);
    } catch (error) {
      console.error('❌ Fatal error in attendance migration:', error.message);
    }
  }

  async migrateCellGroupMembers() {
    console.log('👥 Migrating Cell Group Members...');
    
    try {
      const { data: cellGroupMembers, error } = await this.supabase
        .from('cell_group_members')
        .select('*');

      if (error) {
        console.error('❌ Error fetching cell group members from Supabase:', error.message);
        return;
      }

      console.log(`Found ${cellGroupMembers.length} cell group members to migrate`);
      let migrated = 0;

      for (const cgMember of cellGroupMembers) {
        try {
          await prisma.cellGroupMember.upsert({
            where: { id: cgMember.id },
            update: {
              cell_group_id: cgMember.cell_group_id,
              member_id: cgMember.member_id,
              joined_date: parseDate(cgMember.joined_date) || new Date(),
              status: cgMember.status || 'active',
              created_at: parseDate(cgMember.created_at) || new Date(),
              updated_at: parseDate(cgMember.updated_at) || new Date()
            },
            create: {
              id: cgMember.id,
              cell_group_id: cgMember.cell_group_id,
              member_id: cgMember.member_id,
              joined_date: parseDate(cgMember.joined_date) || new Date(),
              status: cgMember.status || 'active',
              created_at: parseDate(cgMember.created_at) || new Date(),
              updated_at: parseDate(cgMember.updated_at) || new Date()
            }
          });

          migrated++;
          process.stdout.write(`\r✅ Migrated ${migrated}/${cellGroupMembers.length} cell group members`);
        } catch (error) {
          console.error(`\n❌ Error migrating cell group member ${cgMember.id}:`, error.message);
        }
      }

      console.log(`\n✅ Cell Group Members migration completed: ${migrated} migrated\n`);
    } catch (error) {
      console.error('❌ Fatal error in cell group members migration:', error.message);
    }
  }

  async run() {
    try {
      await this.initialize();

      console.log('🔄 Starting migration process...\n');

      // Migration order is important due to foreign key constraints
      await this.migrateDistricts();
      await this.migrateMembers();
      await this.migrateCellGroups();
      await this.migrateCellGroupMembers();
      await this.migrateAttendance();

      console.log('📊 Migration Summary:');
      console.log('===================');
      console.log(`Districts: ${this.stats.districts.migrated}/${this.stats.districts.total} migrated (${this.stats.districts.errors} errors)`);
      console.log(`Members: ${this.stats.members.migrated}/${this.stats.members.total} migrated (${this.stats.members.errors} errors)`);
      console.log(`Cell Groups: ${this.stats.cellGroups.migrated}/${this.stats.cellGroups.total} migrated (${this.stats.cellGroups.errors} errors)`);
      console.log(`Attendance: ${this.stats.attendance.migrated}/${this.stats.attendance.total} migrated (${this.stats.attendance.errors} errors)`);

      console.log('\n🎉 Migration completed successfully!');

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    } finally {
      await prisma.$disconnect();
      rl.close();
    }
  }
}

// Run the migration
const migrator = new SupabaseMigrator();
migrator.run().catch(console.error);
