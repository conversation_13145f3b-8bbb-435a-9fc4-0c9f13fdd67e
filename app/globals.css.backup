@tailwind base;
@tailwind components;
@tailwind utilities;

/* ReactQuill styles */
@import 'react-quill/dist/quill.snow.css';

:root {
  --font-outfit: 'Outfit', sans-serif;

  /* Colors */
  --color-current: currentColor;
  --color-transparent: transparent;
  --color-white: #ffffff;
  --color-black: #101828;

  --color-brand-25: #f2f7ff;
  --color-brand-50: #ecf3ff;
  --color-brand-100: #dde9ff;
  --color-brand-200: #c2d6ff;
  --color-brand-300: #9cb9ff;
  --color-brand-400: #7592ff;
  --color-brand-500: #465fff;
  --color-brand-600: #3641f5;
  --color-brand-700: #2a31d8;
  --color-brand-800: #252dae;
  --color-brand-900: #262e89;
  --color-brand-950: #161950;

  --color-gray-25: #fcfcfd;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f2f4f7;
  --color-gray-200: #e4e7ec;
  --color-gray-300: #d0d5dd;
  --color-gray-400: #98a2b3;
  --color-gray-500: #667085;
  --color-gray-600: #475467;
  --color-gray-700: #344054;
  --color-gray-800: #1d2939;
  --color-gray-900: #101828;
  --color-gray-950: #0c111d;
  --color-gray-dark: #1a2231;

  --color-success-25: #f6fef9;
  --color-success-50: #ecfdf3;
  --color-success-100: #d1fadf;
  --color-success-200: #a6f4c5;
  --color-success-300: #6ce9a6;
  --color-success-400: #32d583;
  --color-success-500: #12b76a;
  --color-success-600: #039855;
  --color-success-700: #027a48;
  --color-success-800: #05603a;
  --color-success-900: #054f31;
  --color-success-950: #053321;

  --color-error-25: #fffbfa;
  --color-error-50: #fef3f2;
  --color-error-100: #fee4e2;
  --color-error-200: #fecdca;
  --color-error-300: #fda29b;
  --color-error-400: #f97066;
  --color-error-500: #f04438;
  --color-error-600: #d92d20;
  --color-error-700: #b42318;
  --color-error-800: #912018;
  --color-error-900: #7a271a;
  --color-error-950: #55160c;

  --color-warning-25: #fffcf5;
  --color-warning-50: #fffaeb;
  --color-warning-100: #fef0c7;
  --color-warning-200: #fedf89;
  --color-warning-300: #fec84b;
  --color-warning-400: #fdb022;
  --color-warning-500: #f79009;
  --color-warning-600: #dc6803;
  --color-warning-700: #b54708;
  --color-warning-800: #93370d;
  --color-warning-900: #7a2e0e;
  --color-warning-950: #4e1d09;

  /* Shadows */
  --shadow-theme-md:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  --shadow-theme-lg:
    0px 12px 16px -4px rgba(16, 24, 40, 0.08),
    0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  --shadow-theme-sm:
    0px 1px 3px 0px rgba(16, 24, 40, 0.1),
    0px 1px 2px 0px rgba(16, 24, 40, 0.06);
  --shadow-theme-xs: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  --shadow-theme-xl:
    0px 20px 24px -4px rgba(16, 24, 40, 0.08),
    0px 8px 8px -4px rgba(16, 24, 40, 0.03);
  --shadow-focus-ring: 0px 0px 0px 4px rgba(70, 95, 255, 0.12);
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
  body {
    position: relative;
    font-weight: 400;
    z-index: 1;
    background-color: #f9fafb;
    font-family: var(--font-outfit);
  }
}

@layer components {
  .btn-primary {
        background-color: var(--color-brand-500);
        color: var(--color-white);
        padding-left: 1rem;
        padding-right: 1rem;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
        border-radius: 0.375rem;
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
        transition-duration: 150ms;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
    .btn-primary:hover {
        background-color: var(--color-brand-600);
  }

  .btn-secondary {
    background-color: #667085; /* Tailwind bg-gray-500 */
    color: #ffffff; /* Tailwind text-white */
    padding-left: 1rem; /* Tailwind px-4 */
    padding-right: 1rem; /* Tailwind px-4 */
    padding-top: 0.5rem; /* Tailwind py-2 */
    padding-bottom: 0.5rem; /* Tailwind py-2 */
    border-radius: 0.375rem; /* Tailwind rounded-md */
    transition-property: background-color;
    transition-duration: 150ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
  .btn-secondary:hover {
    background-color: #475467; /* Tailwind hover:bg-gray-600 */
  }

  .input-field {
    width: 100%;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    outline: none;
    transition: box-shadow 0.2s;
  }
  .input-field:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(70, 95, 255, 0.5);
    border-color: var(--color-brand-500, #465fff);
  }

  .card {
    background-color: #ffffff;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  }

  /* TailAdmin Components */
  .menu-item {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .menu-item-active {
    background-color: var(--color-brand-50);
    color: var(--color-brand-500);
  }
  @media (prefers-color-scheme: dark) {
    .menu-item-active {
      background-color: var(--color-brand-500);
      opacity: 0.1;
      color: var(--color-brand-400);
    }
  }

  .menu-item-inactive {
    color: var(--color-gray-700);
  }
  .menu-item-inactive:hover,
  .menu-item-inactive.group-hover {
    background-color: var(--color-gray-100);
    color: var(--color-gray-700);
  }
  @media (prefers-color-scheme: dark) {
    .menu-item-inactive {
      color: var(--color-gray-300);
    }
    .menu-item-inactive:hover,
    .menu-item-inactive.group-hover {
      background-color: white;
      opacity: 0.05;
      color: var(--color-gray-300);
    }
  }

  .menu-item-icon {
    color: var(--color-gray-500);
    height: 1.25rem;
    width: 1.25rem;
    flex-shrink: 0;
  }

  .menu-item-icon-active {
    color: var(--color-brand-500);
  }
  @media (prefers-color-scheme: dark) {
    .menu-item-icon-active {
      color: var(--color-brand-400);
    }
  }

  .menu-item-icon-inactive {
    color: var(--color-gray-500);
  }
  .menu-item-icon-inactive:hover,
  .menu-item-icon-inactive.group-hover {
    color: var(--color-gray-700);
  }
  @media (prefers-color-scheme: dark) {
    .menu-item-icon-inactive {
      color: var(--color-gray-400);
    }
    .menu-item-icon-inactive:hover,
    .menu-item-icon-inactive.group-hover {
      color: var(--color-gray-300);
    }
  }
  
  .no-scrollbar {
    /* Chrome, Safari and Opera */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 0.375rem;
    height: 0.375rem;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    border-radius: 9999px;
    background-color: var(--color-gray-100);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: var(--color-gray-300);
    border-radius: 9999px;
  }

  @media (prefers-color-scheme: dark) {
    .custom-scrollbar::-webkit-scrollbar-track {
      background-color: var(--color-gray-800);
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: var(--color-gray-700);
    }
  }
  .mobile-nav-button {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    z-index: 9999;
    display: flex;
    height: 3.5rem;
    width: 3.5rem;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    background-color: var(--color-brand-500);
    color: var(--color-white);
    box-shadow: 0 10px 15px -3px rgba(16, 24, 40, 0.1), 0 4px 6px -2px rgba(16, 24, 40, 0.06);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    }
  }
  
  .menu-item-icon-inactive {
    color: var(--color-gray-700);
  }
  }
  @media (min-width: 1024px) {
    .mobile-nav-button {
      display: none;
    }
  }
    color: var(--color-gray-700);
  }
  @media (prefers-color-scheme: dark) {
    .menu-item-icon-inactive {
      color: var(--color-gray-400);
    }
    .menu-item-icon-inactive.group-hover {
      color: var(--color-gray-300);
    }
  }

  /* Collapsed sidebar menu items */
  .sidebar:not(.w-72) .menu-item {
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
  }

  /* Ensure icons maintain their size in collapsed sidebar */
  .sidebar:not(.w-72) .menu-item svg {
    height: 1.25rem;
    width: 1.25rem;
    flex-shrink: 0;
  }

  /* No additional padding needed as the layout handles spacing */

  /* Ensure all sidebar icons have consistent size */
  .sidebar svg {
    height: 1.25rem;
    width: 1.25rem;
    flex-shrink: 0;
  }
  /* Ensure icons maintain their size in collapsed sidebar */
  .sidebar:not(.w-72) .menu-item svg {
    @apply h-5 w-5 flex-shrink-0;
  }

  /* No additional padding needed as the layout handles spacing */

  /* Ensure all sidebar icons have consistent size */
  .sidebar svg {
    @apply h-5 w-5 flex-shrink-0;
  }

  .no-scrollbar {
    /* Chrome, Safari and Opera */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .custom-scrollbar {
    &::-webkit-scrollbar {
      @apply w-1.5 h-1.5;
    }

    &::-webkit-scrollbar-track {
      @apply rounded-full;
    }

    &::-webkit-scrollbar-thumb {
      @apply bg-gray-200 rounded-full dark:bg-gray-700;
    }
  }

  /* Mobile Navigation Floating Button */
  .mobile-nav-button {
    @apply fixed bottom-6 right-6 z-9999 flex h-14 w-14 items-center justify-center rounded-full bg-brand-500 text-white shadow-lg transition-all hover:bg-brand-600 focus:outline-none lg:hidden;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(70, 95, 255, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(70, 95, 255, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(70, 95, 255, 0);
    }
  }
}

/* ReactQuill Editor Customization */
.ql-toolbar {
  border: 1px solid #e5e7eb !important;
  border-bottom: none !important;
  border-radius: 0.375rem 0.375rem 0 0 !important;
  background: #f9fafb !important;
  padding: 8px !important;
}

.ql-container {
  border: 1px solid #e5e7eb !important;
  border-radius: 0 0 0.375rem 0.375rem !important;
  font-family: inherit !important;
}

.ql-editor {
  font-size: 14px !important;
  line-height: 1.5 !important;
  padding: 12px 15px !important;
  min-height: 200px !important;
}

.ql-toolbar .ql-formats {
  margin-right: 15px !important;
}

.ql-toolbar button {
  width: 28px !important;
  height: 28px !important;
  margin: 2px !important;
  border-radius: 4px !important;
  border: none !important;
  background: transparent !important;
}

.ql-toolbar button:hover {
  background: #e5e7eb !important;
}

.ql-toolbar button.ql-active {
  background: #3b82f6 !important;
  color: white !important;
}

.ql-toolbar .ql-stroke {
  stroke: #6b7280 !important;
}

.ql-toolbar .ql-fill {
  fill: #6b7280 !important;
}

.ql-toolbar button.ql-active .ql-stroke {
  stroke: white !important;
}

.ql-toolbar button.ql-active .ql-fill {
  fill: white !important;
}

.ql-toolbar .ql-picker {
  color: #6b7280 !important;
}

.ql-toolbar .ql-picker-options {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.ql-snow .ql-tooltip {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Fix icon sizes - they shouldn't be oversized */
.ql-toolbar button svg {
  width: 18px !important;
  height: 18px !important;
}

.ql-picker .ql-picker-label::before {
  line-height: 28px !important;
}
