-- Add new fields to support attendance-based completion and level progression
-- This migration enhances the class system with attendance tracking and progression logic

-- Add completion_criteria to ClassLevel
ALTER TABLE class_levels ADD COLUMN completion_criteria JSON COMMENT 'Criteria for level completion (attendance_rate, assignments, etc.)';
ALTER TABLE class_levels ADD COLUMN min_attendance_rate DECIMAL(3,2) DEFAULT 0.80 COMMENT 'Minimum attendance rate required to pass (0.80 = 80%)';

-- Add progress tracking to ClassEnrollment  
ALTER TABLE class_enrollments ADD COLUMN attendance_count INT DEFAULT 0 COMMENT 'Number of sessions attended';
ALTER TABLE class_enrollments ADD COLUMN total_sessions INT DEFAULT 0 COMMENT 'Total sessions for this level';
ALTER TABLE class_enrollments ADD COLUMN attendance_rate DECIMAL(3,2) DEFAULT 0.00 COMMENT 'Calculated attendance rate';
ALTER TABLE class_enrollments ADD COLUMN can_progress BOOLEAN DEFAULT FALSE COMMENT 'Whether member can progress to next level';
ALTER TABLE class_enrollments ADD COLUMN progress_notes TEXT COMMENT 'Notes about member progress';

-- Add session attendance tracking table
CREATE TABLE class_session_attendance (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    enrollment_id VARCHAR(36) NOT NULL,
    session_id VARCHAR(36) NOT NULL,
    attendance_meeting_id VARCHAR(36),
    status ENUM('present', 'absent', 'excused', 'late') DEFAULT 'present',
    notes TEXT,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    recorded_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (enrollment_id) REFERENCES class_enrollments(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES class_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (attendance_meeting_id) REFERENCES attendance_meetings(id) ON DELETE SET NULL,
    FOREIGN KEY (recorded_by) REFERENCES members(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_enrollment_session (enrollment_id, session_id),
    INDEX idx_session_attendance (session_id),
    INDEX idx_enrollment_attendance (enrollment_id),
    INDEX idx_attendance_meeting (attendance_meeting_id)
) COMMENT 'Tracks individual member attendance for each class session';

-- Add level completion tracking
CREATE TABLE class_level_completions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    enrollment_id VARCHAR(36) NOT NULL,
    level_id VARCHAR(36) NOT NULL,
    completion_date TIMESTAMP NULL,
    completion_status ENUM('in_progress', 'completed', 'failed', 'dropped') DEFAULT 'in_progress',
    final_attendance_rate DECIMAL(3,2),
    completion_notes TEXT,
    certified_by VARCHAR(36),
    certificate_issued BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (enrollment_id) REFERENCES class_enrollments(id) ON DELETE CASCADE,
    FOREIGN KEY (level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    FOREIGN KEY (certified_by) REFERENCES members(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_enrollment_level (enrollment_id, level_id),
    INDEX idx_level_completions (level_id),
    INDEX idx_enrollment_completions (enrollment_id)
) COMMENT 'Tracks completion status for each level of multi-level classes';
