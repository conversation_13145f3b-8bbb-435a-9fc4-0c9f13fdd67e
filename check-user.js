const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUser() {
  try {
    const user = await prisma.member.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        password_hash: true,
        status: true,
        role: true,
        role_level: true,
        password_reset_required: true,
      }
    });
    
    if (user) {
      console.log('User found:');
      console.log('ID:', user.id);
      console.log('Email:', user.email);
      console.log('Name:', user.first_name, user.last_name);
      console.log('Status:', user.status);
      console.log('Role:', user.role);
      console.log('Role Level:', user.role_level);
      console.log('Has Password Hash:', !!user.password_hash);
      console.log('Password Reset Required:', user.password_reset_required);
      
      if (user.password_hash) {
        console.log('Password Hash (first 50 chars):', user.password_hash.substring(0, 50) + '...');
      }
    } else {
      console.log('User not found with email: <EMAIL>');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUser();
