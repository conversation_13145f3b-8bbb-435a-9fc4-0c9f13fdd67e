// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Member {
  id                       String   @id @default(uuid())
  email                    String   @unique
  first_name               String
  last_name                String
  phone                    String?
  address                  String?
  date_of_birth            DateTime?
  gender                   String?
  marital_status           String?
  occupation               String?
  join_date                DateTime @default(now())
  emergency_contact_name   String?
  emergency_contact_phone  String?
  notes                    String?  @db.Text
  status                   String   @default("active")
  role                     String   @default("member")
  role_level               Int      @default(1)
  role_context             Json?
  password_hash            String?
  password_reset_required  <PERSON><PERSON><PERSON>  @default(false)
  cell_group_id            String?
  district_id              String?
  created_at               DateTime @default(now())
  updated_at               DateTime @updatedAt

  // Relations
  cell_group               CellGroup?              @relation("MemberCellGroup", fields: [cell_group_id], references: [id])
  district                 District?               @relation("MemberDistrict", fields: [district_id], references: [id])
  led_cell_groups          CellGroup[]             @relation("CellGroupLeader")
  assisted_cell_groups     CellGroup[]             @relation("CellGroupAssistantLeader")
  led_districts_1          District[]              @relation("DistrictLeader1")
  led_districts_2          District[]              @relation("DistrictLeader2")
  led_ministries           Ministry[]              @relation("MinistryLeader")
  member_tokens            MemberToken[]
  ministry_memberships     MinistryMember[]
  cell_group_memberships   CellGroupMember[]
  cell_group_leaderships   CellGroupLeader[]
  attendance_participants  AttendanceParticipant[]
  class_enrollments        ClassEnrollment[]
  instructed_sessions      ClassSession[]          @relation("SessionInstructor")
  authored_articles        Article[]               @relation("ArticleAuthor")
  created_meetings         AttendanceMeeting[]     @relation("CreatedMeetings")
  converted_visitors       AttendanceVisitor[]     @relation("ConvertedVisitor")
  article_bookmarks        ArticleBookmark[]
  article_comments         ArticleComment[]
  password_reset_tokens    PasswordResetToken[]
  created_projects         Project[]               @relation("ProjectCreator")

  @@map("members")
}

model MemberToken {
  id           String    @id @default(uuid())
  member_id    String
  token        String    @unique
  created_at   DateTime  @default(now())
  expires_at   DateTime
  last_used_at DateTime?
  is_active    Boolean   @default(true)

  // Relations
  member       Member    @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@map("member_tokens")
}

model District {
  id          String   @id @default(uuid())
  name        String
  description String?  @db.Text
  leader1_id  String?
  leader2_id  String?
  status      String   @default("active")
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  leader1     Member?      @relation("DistrictLeader1", fields: [leader1_id], references: [id])
  leader2     Member?      @relation("DistrictLeader2", fields: [leader2_id], references: [id])
  cell_groups CellGroup[]
  members     Member[]     @relation("MemberDistrict")

  @@map("districts")
}

model CellGroup {
  id                   String   @id @default(uuid())
  name                 String
  description          String?  @db.Text
  district_id          String?
  leader_id            String?
  assistant_leader_id  String?
  meeting_day          String?
  meeting_time         String?
  meeting_location     String?
  status               String   @default("active")
  created_at           DateTime @default(now())
  updated_at           DateTime @updatedAt

  // Relations
  district             District?             @relation(fields: [district_id], references: [id])
  leader               Member?               @relation("CellGroupLeader", fields: [leader_id], references: [id])
  assistant_leader     Member?               @relation("CellGroupAssistantLeader", fields: [assistant_leader_id], references: [id])
  members              Member[]              @relation("MemberCellGroup")
  cell_group_members   CellGroupMember[]
  cell_group_leaders   CellGroupLeader[]
  attendance_meetings  AttendanceMeeting[]

  @@map("cell_groups")
}

model CellGroupMember {
  id            String    @id @default(uuid())
  cell_group_id String
  member_id     String
  joined_date   DateTime  @default(now())
  status        String    @default("active")
  created_at    DateTime  @default(now())
  updated_at    DateTime  @updatedAt

  // Relations
  cell_group    CellGroup @relation(fields: [cell_group_id], references: [id], onDelete: Cascade)
  member        Member    @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@unique([cell_group_id, member_id])
  @@map("cell_group_members")
}

model Ministry {
  id          String   @id @default(uuid())
  name        String
  description String?  @db.Text
  leader_id   String?
  status      String   @default("active")
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  leader               Member?             @relation("MinistryLeader", fields: [leader_id], references: [id])
  ministry_members     MinistryMember[]
  attendance_meetings  AttendanceMeeting[]

  @@map("ministries")
}

model MinistryMember {
  id          String   @id @default(uuid())
  ministry_id String
  member_id   String
  role        String?
  joined_date DateTime @default(now())
  status      String   @default("active")
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  ministry    Ministry @relation(fields: [ministry_id], references: [id], onDelete: Cascade)
  member      Member   @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@unique([ministry_id, member_id])
  @@map("ministry_members")
}

model MinistryRole {
  id            String   @id @default(uuid())
  name          String   @unique
  description   String?  @db.Text
  ministry_type String?  @db.VarChar(50)
  is_leadership Boolean  @default(false)
  sort_order    Int      @default(0)
  status        String   @default("active")
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  @@map("ministry_roles")
}

model AttendanceMeeting {
  id                    String    @id @default(uuid())
  cell_group_id         String?
  meeting_date          DateTime  @db.Date
  meeting_type          String    @default("regular") @db.VarChar(50)
  topic                 String?   @db.VarChar(255)
  notes                 String?   @db.Text
  location              String?   @db.VarChar(255)
  offering              Float?
  created_at            DateTime  @default(now())
  created_by            String?
  ministry_id           String?
  event_category        String?   // cell_group, ministry, service, prayer, class, other
  is_realtime           Boolean   @default(false)
  live_checkin_active   Boolean   @default(false)
  live_checkin_expires_at DateTime?
  qr_code_data          String?   @db.Text
  updated_at            DateTime  @updatedAt

  // Relations
  cell_group       CellGroup?              @relation(fields: [cell_group_id], references: [id])
  ministry         Ministry?               @relation(fields: [ministry_id], references: [id])
  created_by_member Member?                @relation("CreatedMeetings", fields: [created_by], references: [id])
  participants     AttendanceParticipant[]
  visitors         AttendanceVisitor[]
  class_sessions   ClassSession[]

  @@map("attendance_meetings")
}

model AttendanceParticipant {
  id         String   @id @default(uuid())
  meeting_id String
  member_id  String
  status     String   // present, absent, late
  notes      String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relations
  meeting    AttendanceMeeting @relation(fields: [meeting_id], references: [id], onDelete: Cascade)
  member     Member            @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@unique([meeting_id, member_id])
  @@map("attendance_participants")
}

model AttendanceVisitor {
  id                      String   @id @default(uuid())
  meeting_id              String
  first_name              String
  last_name               String
  phone                   String?
  email                   String?
  address                 String?
  notes                   String?
  created_at              DateTime @default(now())
  converted_to_member_id  String?

  // Relations
  meeting                 AttendanceMeeting @relation(fields: [meeting_id], references: [id], onDelete: Cascade)
  converted_to_member     Member?           @relation("ConvertedVisitor", fields: [converted_to_member_id], references: [id])

  @@map("attendance_visitors")
}

model Class {
  id           String   @id @default(uuid())
  name         String
  description  String?  @db.Text
  category     String   // bible_study, counseling, discipleship, leadership, other
  max_students Int?
  status       String   @default("draft") // draft, active, completed, cancelled
  has_levels   Boolean  @default(false)
  instructor   String?
  start_date   DateTime?
  end_date     DateTime?
  max_participants Int?
  schedule     String?
  location     String?
  requirements String?  @db.Text
  materials    String?  @db.Text
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  levels       ClassLevel[]
  enrollments  ClassEnrollment[]
  sessions     ClassSession[]    @relation("ClassDirectSessions")

  @@map("classes")
}

model ClassLevel {
  id                     String   @id @default(uuid())
  class_id               String
  name                   String
  description            String?  @db.Text
  prerequisite_level_id  String?
  order_number           Int
  created_at             DateTime @default(now())
  updated_at             DateTime @updatedAt

  // Relations
  class                  Class         @relation(fields: [class_id], references: [id], onDelete: Cascade)
  prerequisite_level     ClassLevel?   @relation("LevelPrerequisite", fields: [prerequisite_level_id], references: [id])
  dependent_levels       ClassLevel[]  @relation("LevelPrerequisite")
  topics                 ClassTopic[]
  sessions               ClassSession[]
  enrollments            ClassEnrollment[]

  @@map("class_levels")
}

model ClassTopic {
  id               String   @id @default(uuid())
  level_id         String
  name             String
  description      String?  @db.Text
  order_number     Int      @default(1)
  duration_minutes Int?     @default(60)
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  // Relations
  level            ClassLevel @relation(fields: [level_id], references: [id], onDelete: Cascade)

  @@map("class_topics")
}

model ClassSession {
  id                    String   @id @default(uuid())
  class_id              String?
  level_id              String?
  title                 String
  description           String?  @db.Text
  session_date          DateTime
  start_time            String
  end_time              String
  location              String?
  instructor_id         String?
  materials             Json?
  order_number          Int
  attendance_meeting_id String?
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt

  // Relations
  class                 Class?            @relation("ClassDirectSessions", fields: [class_id], references: [id])
  level                 ClassLevel?       @relation(fields: [level_id], references: [id])
  instructor            Member?           @relation("SessionInstructor", fields: [instructor_id], references: [id])
  attendance_meeting    AttendanceMeeting? @relation(fields: [attendance_meeting_id], references: [id])

  @@map("class_sessions")
}

model ClassEnrollment {
  id              String   @id @default(uuid())
  class_id        String
  level_id        String?
  member_id       String
  status          String   @default("enrolled") // enrolled, completed, dropped
  enrolled_at     DateTime @default(now())
  completed_at    DateTime?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  class           Class      @relation(fields: [class_id], references: [id], onDelete: Cascade)
  level           ClassLevel? @relation(fields: [level_id], references: [id])
  member          Member     @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@unique([class_id, member_id])
  @@map("class_enrollments")
}

model Article {
  id           String    @id @default(uuid())
  title        String
  summary      String?   @db.Text
  content      String    @db.LongText
  category     String
  image_url    String?
  status       String    @default("draft") // draft, published, archived
  featured     Boolean   @default(false)
  author_id    String
  published_at DateTime? @default(now())
  view_count   Int       @default(0)
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt

  // Relations
  author       Member            @relation("ArticleAuthor", fields: [author_id], references: [id])
  bookmarks    ArticleBookmark[]
  comments     ArticleComment[]

  @@map("articles")
}

model ArticleBookmark {
  id         String   @id @default(uuid())
  article_id String
  member_id  String
  created_at DateTime @default(now())

  // Relations
  article    Article  @relation(fields: [article_id], references: [id], onDelete: Cascade)
  member     Member   @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@unique([article_id, member_id])
  @@map("article_bookmarks")
}

model ArticleCategory {
  id          String  @id @default(uuid())
  name        String
  description String?
  icon        String?

  @@map("article_categories")
}

model ArticleComment {
  id         String   @id @default(uuid())
  article_id String
  member_id  String
  content    String   @db.Text
  parent_id  String?
  created_at DateTime @default(now())

  // Relations
  article    Article         @relation(fields: [article_id], references: [id], onDelete: Cascade)
  member     Member          @relation(fields: [member_id], references: [id], onDelete: Cascade)
  parent     ArticleComment? @relation("CommentReplies", fields: [parent_id], references: [id])
  replies    ArticleComment[] @relation("CommentReplies")

  @@map("article_comments")
}

model CellGroupLeader {
  id            String   @id @default(uuid())
  cell_group_id String
  member_id     String
  role          String   @default("leader") @db.VarChar(50)
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  // Relations
  cell_group    CellGroup @relation(fields: [cell_group_id], references: [id], onDelete: Cascade)
  member        Member    @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@unique([cell_group_id, member_id])
  @@map("cell_group_leaders")
}

model PasswordResetToken {
  id         String   @id @default(uuid())
  member_id  String
  token      String
  created_at DateTime @default(now())
  expires_at DateTime
  used       Boolean  @default(false)

  // Relations
  member     Member   @relation(fields: [member_id], references: [id], onDelete: Cascade)

  @@map("password_reset_tokens")
}

model Project {
  id                String   @id @default(uuid())
  title             String
  description       String   @db.Text
  image_url         String?
  event_date        DateTime
  target_amount     Decimal  @db.Decimal(15, 2)
  current_amount    Decimal  @db.Decimal(15, 2) @default(0)
  status            String   @default("draft") // draft, published, completed, cancelled
  is_published      Boolean  @default(false)
  created_by        String
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // Relations
  creator           Member      @relation("ProjectCreator", fields: [created_by], references: [id])
  donations         Donation[]

  @@map("projects")
}

model Donation {
  id            String   @id @default(uuid())
  project_id    String
  donor_name    String
  donor_email   String?
  donor_phone   String?
  amount        Decimal  @db.Decimal(15, 2)
  message       String?  @db.Text
  is_anonymous  Boolean  @default(false)
  status        String   @default("confirmed") // pending, confirmed, cancelled
  donated_at    DateTime @default(now())
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  // Relations
  project       Project  @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("donations")
}
