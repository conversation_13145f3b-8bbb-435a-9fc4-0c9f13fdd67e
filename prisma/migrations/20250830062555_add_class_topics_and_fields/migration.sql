/*
  Warnings:

  - You are about to drop the column `completion_date` on the `class_enrollments` table. All the data in the column will be lost.
  - You are about to drop the column `enrollment_date` on the `class_enrollments` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `class_enrollments` DROP COLUMN `completion_date`,
    DROP COLUMN `enrollment_date`,
    ADD COLUMN `completed_at` DATETIME(3) NULL,
    ADD COLUMN `enrolled_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);

-- AlterTable
ALTER TABLE `classes` ADD COLUMN `end_date` DATETIME(3) NULL,
    ADD COLUMN `instructor` VARCHAR(191) NULL,
    ADD COLUMN `location` VARCHAR(191) NULL,
    ADD COLUMN `materials` TEXT NULL,
    ADD COLUMN `max_participants` INTEGER NULL,
    ADD COLUMN `requirements` TEXT NULL,
    ADD COLUMN `schedule` VARCHAR(191) NULL,
    ADD COLUMN `start_date` DATETIME(3) NULL,
    MODIFY `status` VARCHAR(191) NOT NULL DEFAULT 'draft';

-- CreateTable
CREATE TABLE `ministry_roles` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `ministry_type` VARCHAR(50) NULL,
    `is_leadership` BOOLEAN NOT NULL DEFAULT false,
    `sort_order` INTEGER NOT NULL DEFAULT 0,
    `status` VARCHAR(191) NOT NULL DEFAULT 'active',
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ministry_roles_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `class_topics` (
    `id` VARCHAR(191) NOT NULL,
    `level_id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `order_number` INTEGER NOT NULL DEFAULT 1,
    `duration_minutes` INTEGER NULL DEFAULT 60,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `class_topics` ADD CONSTRAINT `class_topics_level_id_fkey` FOREIGN KEY (`level_id`) REFERENCES `class_levels`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
