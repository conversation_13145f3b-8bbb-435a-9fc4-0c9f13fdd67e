{"name": "church-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/line-clamp": "^0.4.4", "@types/lodash": "^4.17.20", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "bcryptjs": "^3.0.2", "eslint": "^8.53.0", "eslint-config-next": "^14.0.2", "html5-qrcode": "^2.3.8", "isomorphic-dompurify": "^2.26.0", "jspdf": "^2.5.1", "jsqr": "^1.4.0", "lodash": "^4.17.21", "lucide-react": "^0.542.0", "next": "^14.0.0", "postcss": "^8.4.31", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-quill": "^2.0.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/prop-types": "^15.7.14", "@types/qrcode": "^1.5.5"}}