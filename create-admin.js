const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    console.log('🔐 Creating admin user...');
    
    // Check if admin already exists
    const existingAdmin = await prisma.member.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (existingAdmin) {
      console.log('Admin user already exists with email: <EMAIL>');
      console.log('Updating password...');
      
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      const updatedUser = await prisma.member.update({
        where: { email: '<EMAIL>' },
        data: {
          password_hash: hashedPassword,
          role: 'admin',
          role_level: 5,
          status: 'active',
          password_reset_required: false
        }
      });
      
      console.log('✅ Admin user updated successfully!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
      return;
    }
    
    // Create new admin user
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    const newAdmin = await prisma.member.create({
      data: {
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
        password_hash: hashedPassword,
        role: 'admin',
        role_level: 5,
        status: 'active',
        password_reset_required: false,
        join_date: new Date()
      }
    });
    
    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Role:', newAdmin.role, 'Level:', newAdmin.role_level);
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
