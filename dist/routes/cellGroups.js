"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const CellGroupController_1 = require("../controllers/CellGroupController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const cellGroupController = new CellGroupController_1.CellGroupController();
router.get("/", auth_1.authenticateToken, cellGroupController.getCellGroups);
router.get("/:id", [auth_1.authenticateToken, (0, express_validator_1.param)("id").isUUID()], cellGroupController.getCellGroupById);
router.post("/", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)("name").isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("description").optional().trim(),
    (0, express_validator_1.body)("district_id").optional().isUUID(),
    (0, express_validator_1.body)("leader_id").optional().isUUID(),
    (0, express_validator_1.body)("assistant_leader_id").optional().isUUID(),
    (0, express_validator_1.body)("meeting_day")
        .optional()
        .isIn([
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    ]),
    (0, express_validator_1.body)("meeting_time")
        .optional()
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
], cellGroupController.createCellGroup);
router.put("/:id", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("name").optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("description").optional().trim(),
    (0, express_validator_1.body)("district_id").optional().isUUID(),
    (0, express_validator_1.body)("leader_id").optional().isUUID(),
    (0, express_validator_1.body)("assistant_leader_id").optional().isUUID(),
    (0, express_validator_1.body)("meeting_day")
        .optional()
        .isIn([
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    ]),
    (0, express_validator_1.body)("meeting_time")
        .optional()
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
], cellGroupController.updateCellGroup);
router.delete("/:id", [auth_1.authenticateToken, auth_1.requireAdmin, (0, express_validator_1.param)("id").isUUID()], cellGroupController.deleteCellGroup);
router.get("/:id/members", [auth_1.authenticateToken, (0, express_validator_1.param)("id").isUUID()], cellGroupController.getCellGroupMembers);
router.post("/:id/members", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("member_ids").isArray(),
    (0, express_validator_1.body)("member_ids.*").isUUID(),
], cellGroupController.addMembersToCellGroup);
router.delete("/:id/members/:memberId", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.param)("memberId").isUUID(),
], cellGroupController.removeMemberFromCellGroup);
router.get("/:id/meetings", [auth_1.authenticateToken, (0, express_validator_1.param)("id").isUUID()], cellGroupController.getCellGroupMeetings);
exports.default = router;
//# sourceMappingURL=cellGroups.js.map