"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const MinistryRoleController_1 = require("../controllers/MinistryRoleController");
const router = express_1.default.Router();
const ministryRoleController = new MinistryRoleController_1.MinistryRoleController();
const createMinistryRoleValidation = [
    (0, express_validator_1.body)("name")
        .notEmpty()
        .withMessage("Role name is required")
        .isLength({ min: 2, max: 100 })
        .withMessage("Role name must be between 2 and 100 characters"),
    (0, express_validator_1.body)("description")
        .optional()
        .isLength({ max: 500 })
        .withMessage("Description cannot exceed 500 characters"),
    (0, express_validator_1.body)("ministry_type")
        .optional()
        .isIn(["worship", "media", "prayer", "general", "hospitality"])
        .withMessage("Ministry type must be one of: worship, media, prayer, general, hospitality"),
    (0, express_validator_1.body)("is_leadership")
        .optional()
        .isBoolean()
        .withMessage("is_leadership must be a boolean value"),
];
const updateMinistryRoleValidation = [
    (0, express_validator_1.body)("name")
        .optional()
        .isLength({ min: 2, max: 100 })
        .withMessage("Role name must be between 2 and 100 characters"),
    (0, express_validator_1.body)("description")
        .optional()
        .isLength({ max: 500 })
        .withMessage("Description cannot exceed 500 characters"),
    (0, express_validator_1.body)("ministry_type")
        .optional()
        .isIn(["worship", "media", "prayer", "general", "hospitality"])
        .withMessage("Ministry type must be one of: worship, media, prayer, general, hospitality"),
    (0, express_validator_1.body)("is_leadership")
        .optional()
        .isBoolean()
        .withMessage("is_leadership must be a boolean value"),
];
router.get("/", auth_1.authenticateToken, ministryRoleController.getMinistryRoles);
router.post("/", auth_1.authenticateToken, auth_1.requireAdmin, createMinistryRoleValidation, ministryRoleController.createMinistryRole);
router.put("/:id", auth_1.authenticateToken, auth_1.requireAdmin, updateMinistryRoleValidation, ministryRoleController.updateMinistryRole);
router.delete("/:id", auth_1.authenticateToken, auth_1.requireAdmin, ministryRoleController.deleteMinistryRole);
exports.default = router;
//# sourceMappingURL=ministryRoles.js.map