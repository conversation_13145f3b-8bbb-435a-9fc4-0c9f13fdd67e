"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const ArticleCategoryController_1 = require("../controllers/ArticleCategoryController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const categoryController = new ArticleCategoryController_1.ArticleCategoryController();
router.get("/", categoryController.getCategories);
router.post("/", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)("name").isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("description").optional().trim(),
    (0, express_validator_1.body)("icon").optional().trim(),
], categoryController.createCategory);
router.put("/:id", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("name").isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("description").optional().trim(),
    (0, express_validator_1.body)("icon").optional().trim(),
], categoryController.updateCategory);
router.delete("/:id", [auth_1.authenticateToken, auth_1.requireAdmin, (0, express_validator_1.param)("id").isUUID()], categoryController.deleteCategory);
exports.default = router;
//# sourceMappingURL=articleCategories.js.map