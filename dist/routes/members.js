"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const MemberController_1 = require("../controllers/MemberController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const memberController = new MemberController_1.MemberController();
router.get('/', auth_1.authenticateToken, memberController.getMembers);
router.get('/:id', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], memberController.getMemberById);
router.post('/', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('first_name').isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('last_name').isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('phone').optional().isMobilePhone('any'),
    (0, express_validator_1.body)('date_of_birth').optional().isISO8601(),
    (0, express_validator_1.body)('gender').optional().isIn(['male', 'female']),
    (0, express_validator_1.body)('marital_status').optional().isIn(['single', 'married', 'divorced', 'widowed']),
], memberController.createMember);
router.put('/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('email').optional().isEmail().normalizeEmail(),
    (0, express_validator_1.body)('first_name').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('last_name').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('phone').optional().isMobilePhone('any'),
    (0, express_validator_1.body)('date_of_birth').optional().isISO8601(),
    (0, express_validator_1.body)('gender').optional().isIn(['male', 'female']),
    (0, express_validator_1.body)('marital_status').optional().isIn(['single', 'married', 'divorced', 'widowed']),
], memberController.updateMember);
router.delete('/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
], memberController.deleteMember);
router.get('/:id/attendance', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], memberController.getMemberAttendance);
router.post('/:id/set-password', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('password').isLength({ min: 8 }),
], memberController.setMemberPassword);
exports.default = router;
//# sourceMappingURL=members.js.map