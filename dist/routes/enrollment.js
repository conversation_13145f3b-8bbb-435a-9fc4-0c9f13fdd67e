"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const ClassEnrollmentController_1 = require("../controllers/ClassEnrollmentController");
const router = (0, express_1.Router)();
const enrollmentController = new ClassEnrollmentController_1.ClassEnrollmentController();
router.get("/classes/:classId/search-members", [
    auth_1.authenticateToken,
    (0, express_validator_1.param)("classId").notEmpty().withMessage("Invalid class ID"),
    (0, express_validator_1.query)("q")
        .isLength({ min: 2 })
        .withMessage("Search query must be at least 2 characters"),
    (0, express_validator_1.query)("limit")
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage("Limit must be between 1 and 50"),
    (0, express_validator_1.query)("exclude_enrolled")
        .optional()
        .isBoolean()
        .withMessage("exclude_enrolled must be boolean"),
], enrollmentController.searchMembers);
router.post("/classes/:classId/batch-enroll", [
    auth_1.authenticateToken,
    (0, express_validator_1.param)("classId").notEmpty().withMessage("Invalid class ID"),
    (0, express_validator_1.body)("member_ids")
        .isArray({ min: 1 })
        .withMessage("member_ids must be a non-empty array"),
    (0, express_validator_1.body)("member_ids").custom((value) => {
        if (!Array.isArray(value)) {
            throw new Error("member_ids must be an array");
        }
        for (const id of value) {
            if (typeof id !== "string" || id.trim().length === 0) {
                throw new Error("Each member_id must be a non-empty string");
            }
        }
        return true;
    }),
    (0, express_validator_1.body)("level_id")
        .optional()
        .notEmpty()
        .withMessage("level_id must be a non-empty string"),
], enrollmentController.batchEnroll);
router.get("/enrollments/:enrollmentId/progress", [
    auth_1.authenticateToken,
    (0, express_validator_1.param)("enrollmentId").notEmpty().withMessage("Invalid enrollment ID"),
], enrollmentController.getEnrollmentProgress);
router.post("/enrollments/:enrollmentId/promote", [
    auth_1.authenticateToken,
    (0, express_validator_1.param)("enrollmentId").notEmpty().withMessage("Invalid enrollment ID"),
    (0, express_validator_1.body)("notes")
        .optional()
        .isString()
        .withMessage("Notes must be a string"),
], enrollmentController.promoteToNextLevel);
exports.default = router;
//# sourceMappingURL=enrollment.js.map