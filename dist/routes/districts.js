"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const DistrictController_1 = require("../controllers/DistrictController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const districtController = new DistrictController_1.DistrictController();
router.get('/', auth_1.authenticateToken, districtController.getDistricts);
router.get('/:id', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], districtController.getDistrictById);
router.post('/', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)('name').isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('description').optional().trim(),
    (0, express_validator_1.body)('leader1_id').optional().isUUID(),
    (0, express_validator_1.body)('leader2_id').optional().isUUID(),
], districtController.createDistrict);
router.put('/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('name').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('description').optional().trim(),
    (0, express_validator_1.body)('leader1_id').optional().isUUID(),
    (0, express_validator_1.body)('leader2_id').optional().isUUID(),
], districtController.updateDistrict);
router.delete('/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
], districtController.deleteDistrict);
router.get('/:id/cell-groups', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], districtController.getDistrictCellGroups);
router.get('/:id/members', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], districtController.getDistrictMembers);
exports.default = router;
//# sourceMappingURL=districts.js.map