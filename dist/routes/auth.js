"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const AuthController_1 = require("../controllers/AuthController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const authController = new AuthController_1.AuthController();
router.post('/admin/login', [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('password').isLength({ min: 6 }),
], authController.adminLogin);
router.post('/member/login', [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('password').isLength({ min: 6 }),
], authController.memberLogin);
router.post('/member/check', [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
], authController.checkMember);
router.post('/member/reset-password', [
    (0, express_validator_1.body)('memberId').isUUID(),
    (0, express_validator_1.body)('currentPassword').isLength({ min: 1 }),
    (0, express_validator_1.body)('newPassword').isLength({ min: 8 }),
], authController.resetPassword);
router.post('/refresh', authController.refreshToken);
router.post('/logout', auth_1.authenticateToken, authController.logout);
router.get('/me', auth_1.authenticateToken, authController.getCurrentUser);
exports.default = router;
//# sourceMappingURL=auth.js.map