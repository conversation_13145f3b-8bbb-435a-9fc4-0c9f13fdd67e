"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const AttendanceController_1 = require("../controllers/AttendanceController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const attendanceController = new AttendanceController_1.AttendanceController();
router.get("/meetings", auth_1.authenticateToken, attendanceController.getMeetings);
router.get("/meetings/:id", [auth_1.authenticateToken, (0, express_validator_1.param)("id").isUUID()], attendanceController.getMeetingById);
router.post("/meetings", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)("event_category").isIn([
        "cell_group",
        "ministry",
        "sunday_service",
        "special_event",
        "prayer",
        "class",
        "other",
    ]),
    (0, express_validator_1.body)("meeting_date").isISO8601(),
    (0, express_validator_1.body)("meeting_type").isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("topic").isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("notes").optional({ nullable: true }).trim(),
    (0, express_validator_1.body)("location").isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("offering").optional({ nullable: true }).isFloat({ min: 0 }),
    (0, express_validator_1.body)("cell_group_id").optional({ nullable: true }).isUUID(),
    (0, express_validator_1.body)("ministry_id").optional({ nullable: true }).isUUID(),
    (0, express_validator_1.body)("participants").optional({ nullable: true }).isArray(),
    (0, express_validator_1.body)("participants.*.member_id").isUUID(),
    (0, express_validator_1.body)("participants.*.status").isIn([
        "present",
        "absent",
        "late",
        "excused",
    ]),
    (0, express_validator_1.body)("visitors").optional({ nullable: true }).isArray(),
    (0, express_validator_1.body)("visitors.*.name").isLength({ min: 1 }).trim(),
], attendanceController.createMeeting);
router.put("/meetings/:id", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("event_category")
        .optional()
        .isIn([
        "cell_group",
        "ministry",
        "sunday_service",
        "special_event",
        "prayer",
        "class",
        "other",
    ]),
    (0, express_validator_1.body)("meeting_date").optional().isISO8601(),
    (0, express_validator_1.body)("meeting_type").optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("topic").optional().trim(),
    (0, express_validator_1.body)("notes").optional({ nullable: true }).trim(),
    (0, express_validator_1.body)("location").optional().trim(),
    (0, express_validator_1.body)("offering").optional({ nullable: true }).isFloat({ min: 0 }),
    (0, express_validator_1.body)("cell_group_id").optional({ nullable: true }).isUUID(),
    (0, express_validator_1.body)("ministry_id").optional({ nullable: true }).isUUID(),
    (0, express_validator_1.body)("participants").optional({ nullable: true }).isArray(),
    (0, express_validator_1.body)("participants.*.member_id").optional().isUUID(),
    (0, express_validator_1.body)("participants.*.status")
        .optional()
        .isIn(["present", "absent", "late", "excused"]),
    (0, express_validator_1.body)("visitors").optional({ nullable: true }).isArray(),
    (0, express_validator_1.body)("visitors.*.name").optional().isLength({ min: 1 }),
], attendanceController.updateMeeting);
router.delete("/meetings/:id", [auth_1.authenticateToken, auth_1.requireAdmin, (0, express_validator_1.param)("id").isUUID()], attendanceController.deleteMeeting);
router.get("/meetings/:id/participants", [auth_1.authenticateToken, (0, express_validator_1.param)("id").isUUID()], attendanceController.getMeetingParticipants);
router.put("/meetings/:id/participants", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("participants").isArray(),
    (0, express_validator_1.body)("participants.*.member_id").isUUID(),
    (0, express_validator_1.body)("participants.*.status").isIn([
        "present",
        "absent",
        "late",
        "excused",
    ]),
], attendanceController.updateMeetingParticipants);
router.get("/meetings/:id/visitors", [auth_1.authenticateToken, (0, express_validator_1.param)("id").isUUID()], attendanceController.getMeetingVisitors);
router.post("/meetings/:id/visitors", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("visitors").isArray(),
    (0, express_validator_1.body)("visitors.*.name").isLength({ min: 1 }).trim(),
], attendanceController.addMeetingVisitors);
router.get("/stats", [
    auth_1.authenticateToken,
    (0, express_validator_1.query)("start_date").optional().isISO8601(),
    (0, express_validator_1.query)("end_date").optional().isISO8601(),
    (0, express_validator_1.query)("event_category")
        .optional()
        .isIn([
        "cell_group",
        "ministry",
        "service",
        "prayer",
        "class",
        "other",
    ]),
], attendanceController.getAttendanceStats);
router.get("/members/:memberId/attendance", [
    auth_1.authenticateToken,
    (0, express_validator_1.param)("memberId").isUUID(),
    (0, express_validator_1.query)("timeFilter")
        .optional()
        .isIn(["all", "month", "quarter", "year"]),
], attendanceController.getMemberAttendance);
router.get("/members/:memberId/history", [auth_1.authenticateToken, (0, express_validator_1.param)("memberId").isUUID()], attendanceController.getMemberAttendanceHistory);
router.patch("/meetings/:id/realtime", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("is_realtime").isBoolean(),
], attendanceController.toggleMeetingRealtime);
router.post("/visitors/:id/convert-to-member", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("cell_group_id").optional({ nullable: true }).isUUID(),
    (0, express_validator_1.body)("district_id").optional({ nullable: true }).isUUID(),
    (0, express_validator_1.body)("baptism_date").optional({ nullable: true }).isISO8601(),
    (0, express_validator_1.body)("join_date").optional({ nullable: true }).isISO8601(),
    (0, express_validator_1.body)("additional_info").optional({ nullable: true }).trim(),
], attendanceController.convertVisitorToMember);
router.get("/visitors/converted", [
    auth_1.authenticateToken,
    (0, express_validator_1.query)("page").optional().isInt({ min: 1 }),
    (0, express_validator_1.query)("limit").optional().isInt({ min: 1, max: 100 }),
], attendanceController.getConvertedVisitors);
router.patch("/meetings/:id/live-attendance", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("active").isBoolean(),
    (0, express_validator_1.body)("expires_at").optional({ nullable: true }).isISO8601(),
], attendanceController.toggleLiveAttendance);
router.get("/meetings/:id/live-status", [auth_1.authenticateToken, (0, express_validator_1.param)("id").isUUID()], attendanceController.getLiveAttendanceStatus);
router.post("/meetings/:id/live-checkin", [auth_1.authenticateToken, (0, express_validator_1.param)("id").isUUID(), (0, express_validator_1.body)("member_id").isUUID()], attendanceController.liveCheckin);
exports.default = router;
//# sourceMappingURL=attendance.js.map