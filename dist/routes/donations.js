"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const DonationController_1 = require("../controllers/DonationController");
const router = (0, express_1.Router)();
router.post('/', DonationController_1.createDonation);
router.get('/project/:project_id', DonationController_1.getDonationsByProject);
router.use(auth_1.authenticateToken);
router.use(auth_1.requireAdmin);
router.get('/', DonationController_1.getDonations);
router.get('/:id', DonationController_1.getDonation);
router.put('/:id', DonationController_1.updateDonation);
router.delete('/:id', DonationController_1.deleteDonation);
exports.default = router;
//# sourceMappingURL=donations.js.map