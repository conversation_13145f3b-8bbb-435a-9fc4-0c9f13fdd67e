"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const ArticleController_1 = require("../controllers/ArticleController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const articleController = new ArticleController_1.ArticleController();
router.get("/", articleController.getArticles);
router.get("/:id", [(0, express_validator_1.param)("id").isUUID()], articleController.getArticleById);
router.post("/", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)("title").isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("summary").optional().trim(),
    (0, express_validator_1.body)("content").isLength({ min: 1 }),
    (0, express_validator_1.body)("category").isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("image_url")
        .optional()
        .custom((value) => {
        if (!value || value === "")
            return true;
        try {
            new URL(value);
            return true;
        }
        catch {
            throw new Error("Invalid URL format");
        }
    }),
    (0, express_validator_1.body)("status").optional().isIn(["draft", "published", "archived"]),
    (0, express_validator_1.body)("featured").optional().isBoolean(),
], articleController.createArticle);
router.put("/:id", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("title").optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("summary").optional().trim(),
    (0, express_validator_1.body)("content").optional().isLength({ min: 1 }),
    (0, express_validator_1.body)("category").optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)("image_url")
        .optional()
        .custom((value) => {
        if (!value || value === "")
            return true;
        try {
            new URL(value);
            return true;
        }
        catch {
            throw new Error("Invalid URL format");
        }
    }),
    (0, express_validator_1.body)("status").optional().isIn(["draft", "published", "archived"]),
    (0, express_validator_1.body)("featured").optional().isBoolean(),
], articleController.updateArticle);
router.delete("/:id", [auth_1.authenticateToken, auth_1.requireAdmin, (0, express_validator_1.param)("id").isUUID()], articleController.deleteArticle);
router.patch("/:id/publish", [auth_1.authenticateToken, auth_1.requireAdmin, (0, express_validator_1.param)("id").isUUID()], articleController.publishArticle);
router.patch("/:id/unpublish", [auth_1.authenticateToken, auth_1.requireAdmin, (0, express_validator_1.param)("id").isUUID()], articleController.unpublishArticle);
router.patch("/:id/featured", [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)("id").isUUID(),
    (0, express_validator_1.body)("featured").isBoolean(),
], articleController.toggleFeatured);
router.post("/:id/view", [(0, express_validator_1.param)("id").isUUID()], articleController.incrementViewCount);
router.get("/featured/list", articleController.getFeaturedArticles);
router.get("/category/:category", [(0, express_validator_1.param)("category").isLength({ min: 1 }).trim()], articleController.getArticlesByCategory);
router.get("/categories/list", articleController.getCategories);
exports.default = router;
//# sourceMappingURL=articles.js.map