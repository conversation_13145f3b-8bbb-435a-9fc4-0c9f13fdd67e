"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const MinistryController_1 = require("../controllers/MinistryController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const ministryController = new MinistryController_1.MinistryController();
router.get('/', auth_1.authenticateToken, ministryController.getMinistries);
router.get('/:id', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], ministryController.getMinistryById);
router.post('/', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)('name').isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('description').optional().trim(),
    (0, express_validator_1.body)('leader_id').optional().isUUID(),
], ministryController.createMinistry);
router.put('/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
], ministryController.updateMinistry);
router.delete('/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
], ministryController.deleteMinistry);
router.get('/:id/members', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], ministryController.getMinistryMembers);
router.post('/:id/members', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('member_ids').isArray(),
    (0, express_validator_1.body)('member_ids.*').isUUID(),
    (0, express_validator_1.body)('role').optional().trim(),
], ministryController.addMembersToMinistry);
router.put('/:id/members/:memberId', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.param)('memberId').isUUID(),
    (0, express_validator_1.body)('role').optional().trim(),
    (0, express_validator_1.body)('status').optional().isIn(['active', 'inactive']),
], ministryController.updateMinistryMember);
router.delete('/:id/members/:memberId', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.param)('memberId').isUUID(),
], ministryController.removeMemberFromMinistry);
router.get('/:id/meetings', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], ministryController.getMinistryMeetings);
exports.default = router;
//# sourceMappingURL=ministries.js.map