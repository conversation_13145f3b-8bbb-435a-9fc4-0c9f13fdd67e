import { Response, NextFunction } from "express";
import { AuthRequest } from "../middleware/auth";
export declare class MinistryRoleController {
    static autoCreateRoleIfNeeded(roleName: string): Promise<void>;
    getMinistryRoles(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createMinistryRole(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateMinistryRole(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteMinistryRole(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=MinistryRoleController.d.ts.map