"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDonationsByProject = exports.deleteDonation = exports.updateDonation = exports.createDonation = exports.getDonation = exports.getDonations = void 0;
const client_1 = require("@prisma/client");
const errorHandler_1 = require("../middleware/errorHandler");
const prisma = new client_1.PrismaClient();
const getDonations = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const project_id = req.query.project_id;
        const status = req.query.status;
        const search = req.query.search;
        const skip = (page - 1) * limit;
        const where = {};
        if (project_id) {
            where.project_id = project_id;
        }
        if (status) {
            where.status = status;
        }
        if (search) {
            where.OR = [
                { donor_name: { contains: search } },
                { donor_email: { contains: search } },
            ];
        }
        const total = await prisma.donation.count({ where });
        const donations = await prisma.donation.findMany({
            where,
            include: {
                project: {
                    select: {
                        id: true,
                        title: true,
                        status: true,
                    },
                },
            },
            orderBy: { donated_at: "desc" },
            skip,
            take: limit,
        });
        const totalPages = Math.ceil(total / limit);
        res.json({
            success: true,
            data: donations,
            pagination: {
                page,
                limit,
                total,
                totalPages,
            },
        });
    }
    catch (error) {
        console.error("Error fetching donations:", error);
        throw (0, errorHandler_1.createError)("Failed to fetch donations", 500);
    }
};
exports.getDonations = getDonations;
const getDonation = async (req, res) => {
    try {
        const { id } = req.params;
        const donation = await prisma.donation.findUnique({
            where: { id },
            include: {
                project: {
                    select: {
                        id: true,
                        title: true,
                        description: true,
                        target_amount: true,
                        status: true,
                    },
                },
            },
        });
        if (!donation) {
            throw (0, errorHandler_1.createError)("Donation not found", 404);
        }
        res.json({
            success: true,
            data: donation,
        });
    }
    catch (error) {
        console.error("Error fetching donation:", error);
        throw (0, errorHandler_1.createError)("Failed to fetch donation", 500);
    }
};
exports.getDonation = getDonation;
const createDonation = async (req, res) => {
    try {
        const { project_id, donor_name, donor_email, donor_phone, amount, message, is_anonymous = false, status = "confirmed", } = req.body;
        if (!project_id || !donor_name || !amount) {
            throw (0, errorHandler_1.createError)("Missing required fields: project_id, donor_name, amount", 400);
        }
        if (Number(amount) <= 0) {
            throw (0, errorHandler_1.createError)("Donation amount must be greater than 0", 400);
        }
        const project = await prisma.project.findUnique({
            where: { id: project_id },
        });
        if (!project) {
            throw (0, errorHandler_1.createError)("Project not found", 404);
        }
        if (!project.is_published) {
            throw (0, errorHandler_1.createError)("Cannot donate to unpublished project", 400);
        }
        if (project.status === "completed" || project.status === "cancelled") {
            throw (0, errorHandler_1.createError)("Cannot donate to completed or cancelled project", 400);
        }
        const donation = await prisma.donation.create({
            data: {
                project_id,
                donor_name,
                donor_email,
                donor_phone,
                amount: Number(amount),
                message,
                is_anonymous,
                status,
            },
            include: {
                project: {
                    select: {
                        id: true,
                        title: true,
                        target_amount: true,
                    },
                },
            },
        });
        if (status === "confirmed") {
            await updateProjectCurrentAmount(project_id);
        }
        res.status(201).json({
            success: true,
            data: donation,
        });
    }
    catch (error) {
        console.error("Error creating donation:", error);
        throw (0, errorHandler_1.createError)("Failed to create donation", 500);
    }
};
exports.createDonation = createDonation;
const updateDonation = async (req, res) => {
    try {
        const { id } = req.params;
        const { donor_name, donor_email, donor_phone, amount, message, is_anonymous, status, } = req.body;
        const existingDonation = await prisma.donation.findUnique({
            where: { id },
        });
        if (!existingDonation) {
            throw (0, errorHandler_1.createError)("Donation not found", 404);
        }
        if (amount !== undefined && Number(amount) <= 0) {
            throw (0, errorHandler_1.createError)("Donation amount must be greater than 0", 400);
        }
        const updateData = {};
        if (donor_name !== undefined)
            updateData.donor_name = donor_name;
        if (donor_email !== undefined)
            updateData.donor_email = donor_email;
        if (donor_phone !== undefined)
            updateData.donor_phone = donor_phone;
        if (amount !== undefined)
            updateData.amount = Number(amount);
        if (message !== undefined)
            updateData.message = message;
        if (is_anonymous !== undefined)
            updateData.is_anonymous = is_anonymous;
        if (status !== undefined)
            updateData.status = status;
        const donation = await prisma.donation.update({
            where: { id },
            data: updateData,
            include: {
                project: {
                    select: {
                        id: true,
                        title: true,
                        target_amount: true,
                    },
                },
            },
        });
        if (status !== undefined) {
            await updateProjectCurrentAmount(existingDonation.project_id);
        }
        res.json({
            success: true,
            data: donation,
        });
    }
    catch (error) {
        console.error("Error updating donation:", error);
        throw (0, errorHandler_1.createError)("Failed to update donation", 500);
    }
};
exports.updateDonation = updateDonation;
const deleteDonation = async (req, res) => {
    try {
        const { id } = req.params;
        const existingDonation = await prisma.donation.findUnique({
            where: { id },
        });
        if (!existingDonation) {
            throw (0, errorHandler_1.createError)("Donation not found", 404);
        }
        const project_id = existingDonation.project_id;
        await prisma.donation.delete({
            where: { id },
        });
        await updateProjectCurrentAmount(project_id);
        res.json({
            success: true,
            message: "Donation deleted successfully",
        });
    }
    catch (error) {
        console.error("Error deleting donation:", error);
        throw (0, errorHandler_1.createError)("Failed to delete donation", 500);
    }
};
exports.deleteDonation = deleteDonation;
const getDonationsByProject = async (req, res) => {
    try {
        const { project_id } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status;
        const skip = (page - 1) * limit;
        const project = await prisma.project.findUnique({
            where: { id: project_id },
        });
        if (!project) {
            throw (0, errorHandler_1.createError)("Project not found", 404);
        }
        const where = {
            project_id,
        };
        if (status && status !== "all") {
            where.status = status;
        }
        const total = await prisma.donation.count({ where });
        const donations = await prisma.donation.findMany({
            where,
            select: {
                id: true,
                donor_name: true,
                donor_email: true,
                amount: true,
                message: true,
                is_anonymous: true,
                status: true,
                donated_at: true,
            },
            orderBy: { donated_at: "desc" },
            skip,
            take: limit,
        });
        const totalPages = Math.ceil(total / limit);
        res.json({
            success: true,
            data: donations,
            pagination: {
                page,
                limit,
                total,
                totalPages,
            },
        });
    }
    catch (error) {
        console.error("Error fetching project donations:", error);
        throw (0, errorHandler_1.createError)("Failed to fetch project donations", 500);
    }
};
exports.getDonationsByProject = getDonationsByProject;
async function updateProjectCurrentAmount(project_id) {
    try {
        const result = await prisma.donation.aggregate({
            where: {
                project_id,
                status: "confirmed",
            },
            _sum: {
                amount: true,
            },
        });
        const current_amount = result._sum.amount || 0;
        await prisma.project.update({
            where: { id: project_id },
            data: { current_amount: Number(current_amount) },
        });
    }
    catch (error) {
        console.error("Error updating project current amount:", error);
    }
}
//# sourceMappingURL=DonationController.js.map