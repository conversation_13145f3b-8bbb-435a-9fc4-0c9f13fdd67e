import { Response, NextFunction } from "express";
import { AuthRequest } from "../middleware/auth";
export declare class ClassEnrollmentController {
    searchMembers(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    batchEnroll(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getEnrollmentProgress(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    promoteToNextLevel(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=ClassEnrollmentController.d.ts.map