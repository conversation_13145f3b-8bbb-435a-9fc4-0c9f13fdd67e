import { Request, Response } from "express";
export declare const getProjects: (req: Request, res: Response) => Promise<void>;
export declare const getProject: (req: Request, res: Response) => Promise<void>;
export declare const createProject: (req: Request, res: Response) => Promise<void>;
export declare const updateProject: (req: Request, res: Response) => Promise<void>;
export declare const deleteProject: (req: Request, res: Response) => Promise<void>;
export declare const getPublishedProjects: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=ProjectController.d.ts.map