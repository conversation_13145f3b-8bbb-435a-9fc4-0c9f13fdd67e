"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPublishedProjects = exports.deleteProject = exports.updateProject = exports.createProject = exports.getProject = exports.getProjects = void 0;
const client_1 = require("@prisma/client");
const errorHandler_1 = require("../middleware/errorHandler");
const prisma = new client_1.PrismaClient();
const getProjects = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status;
        const is_published = req.query.is_published;
        const search = req.query.search;
        const skip = (page - 1) * limit;
        const where = {};
        if (status) {
            where.status = status;
        }
        if (is_published !== undefined) {
            where.is_published = is_published === "true";
        }
        if (search) {
            where.OR = [
                { title: { contains: search } },
                { description: { contains: search } },
            ];
        }
        const total = await prisma.project.count({ where });
        const projects = await prisma.project.findMany({
            where,
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                donations: {
                    where: { status: "confirmed" },
                    select: {
                        id: true,
                        donor_name: true,
                        amount: true,
                        is_anonymous: true,
                        donated_at: true,
                    },
                    orderBy: { donated_at: "desc" },
                },
                _count: {
                    select: {
                        donations: {
                            where: { status: "confirmed" },
                        },
                    },
                },
            },
            orderBy: { created_at: "desc" },
            skip,
            take: limit,
        });
        const projectsWithProgress = projects.map((project) => {
            const confirmedDonations = project.donations.filter((d) => d);
            const totalDonated = confirmedDonations.reduce((sum, donation) => sum + Number(donation.amount), 0);
            const progress = Number(project.target_amount) > 0
                ? (totalDonated / Number(project.target_amount)) * 100
                : 0;
            return {
                ...project,
                current_amount: totalDonated,
                progress: Math.min(progress, 100),
                donations_count: project._count.donations,
            };
        });
        const totalPages = Math.ceil(total / limit);
        res.json({
            success: true,
            data: projectsWithProgress,
            pagination: {
                page,
                limit,
                total,
                totalPages,
            },
        });
    }
    catch (error) {
        console.error("Error fetching projects:", error);
        throw (0, errorHandler_1.createError)("Failed to fetch projects", 500);
    }
};
exports.getProjects = getProjects;
const getProject = async (req, res) => {
    try {
        const { id } = req.params;
        const project = await prisma.project.findUnique({
            where: { id },
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                donations: {
                    where: { status: "confirmed" },
                    select: {
                        id: true,
                        donor_name: true,
                        donor_email: true,
                        amount: true,
                        message: true,
                        is_anonymous: true,
                        donated_at: true,
                    },
                    orderBy: { donated_at: "desc" },
                },
            },
        });
        if (!project) {
            throw (0, errorHandler_1.createError)("Project not found", 404);
        }
        const totalDonated = project.donations.reduce((sum, donation) => sum + Number(donation.amount), 0);
        const progress = Number(project.target_amount) > 0
            ? (totalDonated / Number(project.target_amount)) * 100
            : 0;
        const projectWithProgress = {
            ...project,
            current_amount: totalDonated,
            progress: Math.min(progress, 100),
            donations_count: project.donations.length,
        };
        res.json({
            success: true,
            data: projectWithProgress,
        });
    }
    catch (error) {
        console.error("Error fetching project:", error);
        throw (0, errorHandler_1.createError)("Failed to fetch project", 500);
    }
};
exports.getProject = getProject;
const createProject = async (req, res) => {
    try {
        const { title, description, image_url, event_date, target_amount, status = "draft", is_published = false, } = req.body;
        if (!title || !description || !event_date || !target_amount) {
            throw (0, errorHandler_1.createError)("Missing required fields: title, description, event_date, target_amount", 400);
        }
        if (Number(target_amount) <= 0) {
            throw (0, errorHandler_1.createError)("Target amount must be greater than 0", 400);
        }
        const created_by = req.user.id;
        const project = await prisma.project.create({
            data: {
                title,
                description,
                image_url,
                event_date: new Date(event_date),
                target_amount: Number(target_amount),
                status,
                is_published,
                created_by,
            },
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
        });
        res.status(201).json({
            success: true,
            data: {
                ...project,
                current_amount: 0,
                progress: 0,
                donations_count: 0,
            },
        });
    }
    catch (error) {
        console.error("Error creating project:", error);
        throw (0, errorHandler_1.createError)("Failed to create project", 500);
    }
};
exports.createProject = createProject;
const updateProject = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description, image_url, event_date, target_amount, status, is_published, } = req.body;
        const existingProject = await prisma.project.findUnique({
            where: { id },
        });
        if (!existingProject) {
            throw (0, errorHandler_1.createError)("Project not found", 404);
        }
        if (target_amount !== undefined && Number(target_amount) <= 0) {
            throw (0, errorHandler_1.createError)("Target amount must be greater than 0", 400);
        }
        const updateData = {};
        if (title !== undefined)
            updateData.title = title;
        if (description !== undefined)
            updateData.description = description;
        if (image_url !== undefined)
            updateData.image_url = image_url;
        if (event_date !== undefined)
            updateData.event_date = new Date(event_date);
        if (target_amount !== undefined)
            updateData.target_amount = Number(target_amount);
        if (status !== undefined)
            updateData.status = status;
        if (is_published !== undefined)
            updateData.is_published = is_published;
        const project = await prisma.project.update({
            where: { id },
            data: updateData,
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                donations: {
                    where: { status: "confirmed" },
                    select: {
                        amount: true,
                    },
                },
            },
        });
        const totalDonated = project.donations.reduce((sum, donation) => sum + Number(donation.amount), 0);
        const progress = Number(project.target_amount) > 0
            ? (totalDonated / Number(project.target_amount)) * 100
            : 0;
        res.json({
            success: true,
            data: {
                ...project,
                current_amount: totalDonated,
                progress: Math.min(progress, 100),
                donations_count: project.donations.length,
            },
        });
    }
    catch (error) {
        console.error("Error updating project:", error);
        throw (0, errorHandler_1.createError)("Failed to update project", 500);
    }
};
exports.updateProject = updateProject;
const deleteProject = async (req, res) => {
    try {
        const { id } = req.params;
        const existingProject = await prisma.project.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        donations: true,
                    },
                },
            },
        });
        if (!existingProject) {
            throw (0, errorHandler_1.createError)("Project not found", 404);
        }
        if (existingProject._count.donations > 0) {
            throw (0, errorHandler_1.createError)("Cannot delete project with existing donations", 400);
        }
        await prisma.project.delete({
            where: { id },
        });
        res.json({
            success: true,
            message: "Project deleted successfully",
        });
    }
    catch (error) {
        console.error("Error deleting project:", error);
        throw (0, errorHandler_1.createError)("Failed to delete project", 500);
    }
};
exports.deleteProject = deleteProject;
const getPublishedProjects = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const search = req.query.search;
        const skip = (page - 1) * limit;
        const where = {
            is_published: true,
            status: { not: "cancelled" },
        };
        if (search) {
            where.OR = [
                { title: { contains: search } },
                { description: { contains: search } },
            ];
        }
        const total = await prisma.project.count({ where });
        const projects = await prisma.project.findMany({
            where,
            include: {
                creator: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                donations: {
                    where: { status: "confirmed" },
                    select: {
                        id: true,
                        donor_name: true,
                        amount: true,
                        is_anonymous: true,
                        donated_at: true,
                    },
                    orderBy: { donated_at: "desc" },
                },
                _count: {
                    select: {
                        donations: {
                            where: { status: "confirmed" },
                        },
                    },
                },
            },
            orderBy: { created_at: "desc" },
            skip,
            take: limit,
        });
        const projectsWithProgress = projects.map((project) => {
            const confirmedDonations = project.donations.filter((d) => d);
            const totalDonated = confirmedDonations.reduce((sum, donation) => sum + Number(donation.amount), 0);
            const progress = Number(project.target_amount) > 0
                ? (totalDonated / Number(project.target_amount)) * 100
                : 0;
            return {
                ...project,
                current_amount: totalDonated,
                progress: Math.min(progress, 100),
                donations_count: project._count.donations,
            };
        });
        const totalPages = Math.ceil(total / limit);
        res.json({
            success: true,
            data: projectsWithProgress,
            pagination: {
                page,
                limit,
                total,
                totalPages,
            },
        });
    }
    catch (error) {
        console.error("Error fetching published projects:", error);
        throw (0, errorHandler_1.createError)("Failed to fetch published projects", 500);
    }
};
exports.getPublishedProjects = getPublishedProjects;
//# sourceMappingURL=ProjectController.js.map