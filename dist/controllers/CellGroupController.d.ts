import { Response, NextFunction } from "express";
import { AuthRequest } from "../middleware/auth";
export declare class CellGroupController {
    getCellGroups(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getCellGroupById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createCellGroup(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateCellGroup(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteCellGroup(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getCellGroupMembers(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    addMembersToCellGroup(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    removeMemberFromCellGroup(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getCellGroupMeetings(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=CellGroupController.d.ts.map