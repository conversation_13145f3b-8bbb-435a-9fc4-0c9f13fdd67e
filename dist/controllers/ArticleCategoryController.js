"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleCategoryController = void 0;
const express_validator_1 = require("express-validator");
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
class ArticleCategoryController {
    async getCategories(req, res, next) {
        try {
            const dbCategories = await database_1.prisma.articleCategory.findMany({
                select: {
                    id: true,
                    name: true,
                    description: true,
                    icon: true,
                },
                orderBy: { name: "asc" },
            });
            const articleCategories = await database_1.prisma.article.findMany({
                select: { category: true },
                distinct: ["category"],
                where: {
                    category: { not: "" },
                },
            });
            const allCategories = new Set();
            dbCategories.forEach((cat) => allCategories.add(cat.name));
            articleCategories.forEach((article) => {
                if (article.category) {
                    allCategories.add(article.category);
                }
            });
            const categories = Array.from(allCategories).map((name) => {
                const dbCategory = dbCategories.find((cat) => cat.name === name);
                return dbCategory || { name, description: null, icon: null };
            });
            res.json({ success: true, data: categories });
        }
        catch (error) {
            next(error);
        }
    }
    async createCategory(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { name, description, icon } = req.body;
            const existingCategory = await database_1.prisma.articleCategory.findFirst({
                where: { name },
            });
            if (existingCategory) {
                throw (0, errorHandler_1.createError)("Category already exists", 409);
            }
            const category = await database_1.prisma.articleCategory.create({
                data: { name, description, icon },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    icon: true,
                },
            });
            res.status(201).json({ success: true, data: category });
        }
        catch (error) {
            next(error);
        }
    }
    async updateCategory(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const { name, description, icon } = req.body;
            const category = await database_1.prisma.articleCategory.update({
                where: { id },
                data: { name, description, icon },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    icon: true,
                },
            });
            res.json({ success: true, data: category });
        }
        catch (error) {
            if (error.code === "P2025") {
                next((0, errorHandler_1.createError)("Category not found", 404));
            }
            else {
                next(error);
            }
        }
    }
    async deleteCategory(req, res, next) {
        try {
            const { id } = req.params;
            const articleCount = await database_1.prisma.article.count({
                where: {
                    category: {
                        in: await database_1.prisma.articleCategory
                            .findUnique({
                            where: { id },
                            select: { name: true },
                        })
                            .then((cat) => (cat ? [cat.name] : [])),
                    },
                },
            });
            if (articleCount > 0) {
                throw (0, errorHandler_1.createError)("Cannot delete category that is being used by articles", 400);
            }
            await database_1.prisma.articleCategory.delete({ where: { id } });
            res.json({
                success: true,
                message: "Category deleted successfully",
            });
        }
        catch (error) {
            if (error.code === "P2025") {
                next((0, errorHandler_1.createError)("Category not found", 404));
            }
            else {
                next(error);
            }
        }
    }
}
exports.ArticleCategoryController = ArticleCategoryController;
//# sourceMappingURL=ArticleCategoryController.js.map