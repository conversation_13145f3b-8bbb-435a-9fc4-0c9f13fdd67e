import { Request, Response, NextFunction } from "express";
import { AuthRequest } from "../middleware/auth";
export declare class ArticleCategoryController {
    getCategories(req: Request, res: Response, next: NextFunction): Promise<void>;
    createCategory(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateCategory(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteCategory(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=ArticleCategoryController.d.ts.map