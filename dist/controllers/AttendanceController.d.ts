import { Response, NextFunction } from "express";
import { AuthRequest } from "../middleware/auth";
export declare class AttendanceController {
    getMeetings(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMeetingById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createMeeting(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateMeeting(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteMeeting(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMeetingParticipants(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateMeetingParticipants(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMeetingVisitors(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    addMeetingVisitors(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getAttendanceStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMemberAttendanceHistory(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    toggleMeetingRealtime(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMemberAttendance(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    convertVisitorToMember(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getConvertedVisitors(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    toggleLiveAttendance(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getLiveAttendanceStatus(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    liveCheckin(req: AuthRequest, res: Response, next: NextFunction): Promise<Response<any, Record<string, any>> | undefined>;
}
//# sourceMappingURL=AttendanceController.d.ts.map