"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MinistryController = void 0;
const express_validator_1 = require("express-validator");
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
const MinistryRoleController_1 = require("./MinistryRoleController");
class MinistryController {
    async getMinistries(req, res, next) {
        try {
            const { page = 1, limit = 10, search } = req.query;
            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);
            const where = { status: "active" };
            if (search) {
                where.OR = [
                    { name: { contains: search } },
                    { description: { contains: search } },
                ];
            }
            const [ministries, total] = await Promise.all([
                database_1.prisma.ministry.findMany({
                    where,
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        status: true,
                        created_at: true,
                        leader: {
                            select: {
                                id: true,
                                first_name: true,
                                last_name: true,
                            },
                        },
                        _count: {
                            select: {
                                ministry_members: {
                                    where: { status: "active" },
                                },
                            },
                        },
                    },
                    orderBy: { name: "asc" },
                    skip,
                    take,
                }),
                database_1.prisma.ministry.count({ where }),
            ]);
            res.json({
                success: true,
                data: ministries,
                pagination: {
                    page,
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getMinistryById(req, res, next) {
        try {
            const { id } = req.params;
            const ministry = await database_1.prisma.ministry.findUnique({
                where: { id },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    status: true,
                    created_at: true,
                    updated_at: true,
                    leader: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                        },
                    },
                    ministry_members: {
                        select: {
                            id: true,
                            role: true,
                            joined_date: true,
                            status: true,
                            member: {
                                select: {
                                    id: true,
                                    first_name: true,
                                    last_name: true,
                                    email: true,
                                    phone: true,
                                },
                            },
                        },
                        where: { status: "active" },
                        orderBy: { member: { last_name: "asc" } },
                    },
                    _count: {
                        select: {
                            ministry_members: { where: { status: "active" } },
                            attendance_meetings: true,
                        },
                    },
                },
            });
            if (!ministry) {
                throw (0, errorHandler_1.createError)("Ministry not found", 404);
            }
            res.json({ success: true, data: ministry });
        }
        catch (error) {
            next(error);
        }
    }
    async createMinistry(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const ministryData = req.body;
            if (ministryData.leader_id) {
                const leader = await database_1.prisma.member.findUnique({
                    where: { id: ministryData.leader_id },
                });
                if (!leader) {
                    throw (0, errorHandler_1.createError)("Leader not found", 400);
                }
            }
            const ministry = await database_1.prisma.ministry.create({
                data: ministryData,
            });
            res.status(201).json({ success: true, data: ministry });
        }
        catch (error) {
            next(error);
        }
    }
    async updateMinistry(req, res, next) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const ministry = await database_1.prisma.ministry.update({
                where: { id },
                data: updateData,
            });
            res.json({ success: true, data: ministry });
        }
        catch (error) {
            next(error);
        }
    }
    async deleteMinistry(req, res, next) {
        try {
            const { id } = req.params;
            await database_1.prisma.ministry.update({
                where: { id },
                data: { status: "inactive" },
            });
            res.json({
                success: true,
                data: { message: "Ministry deleted successfully" },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getMinistryMembers(req, res, next) {
        try {
            const { id } = req.params;
            const members = await database_1.prisma.ministryMember.findMany({
                where: {
                    ministry_id: id,
                    status: "active",
                },
                select: {
                    id: true,
                    role: true,
                    joined_date: true,
                    status: true,
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                            status: true,
                        },
                    },
                },
                orderBy: { member: { last_name: "asc" } },
            });
            res.json({ success: true, data: members });
        }
        catch (error) {
            next(error);
        }
    }
    async addMembersToMinistry(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const { member_ids, role } = req.body;
            const ministry = await database_1.prisma.ministry.findUnique({
                where: { id },
            });
            if (!ministry) {
                throw (0, errorHandler_1.createError)("Ministry not found", 404);
            }
            if (role && typeof role === "string" && role.trim()) {
                await MinistryRoleController_1.MinistryRoleController.autoCreateRoleIfNeeded(role.trim());
            }
            const memberships = member_ids.map((member_id) => ({
                ministry_id: id,
                member_id,
                role: role || null,
            }));
            await database_1.prisma.ministryMember.createMany({
                data: memberships,
                skipDuplicates: true,
            });
            res.json({
                success: true,
                data: { message: "Members added successfully" },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async updateMinistryMember(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id, memberId } = req.params;
            const updateData = req.body;
            const membership = await database_1.prisma.ministryMember.updateMany({
                where: {
                    ministry_id: id,
                    member_id: memberId,
                },
                data: updateData,
            });
            if (membership.count === 0) {
                throw (0, errorHandler_1.createError)("Ministry membership not found", 404);
            }
            res.json({
                success: true,
                data: { message: "Ministry member updated successfully" },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async removeMemberFromMinistry(req, res, next) {
        try {
            const { id, memberId } = req.params;
            await database_1.prisma.ministryMember.updateMany({
                where: {
                    ministry_id: id,
                    member_id: memberId,
                },
                data: { status: "inactive" },
            });
            res.json({
                success: true,
                data: { message: "Member removed successfully" },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getMinistryMeetings(req, res, next) {
        try {
            const { id } = req.params;
            const { page = 1, limit = 10 } = req.query;
            const skip = (Number(page) - 1) * Number(limit);
            const take = Math.min(Number(limit), 100);
            const [meetings, total] = await Promise.all([
                database_1.prisma.attendanceMeeting.findMany({
                    where: {
                        ministry_id: id,
                        event_category: "ministry",
                    },
                    select: {
                        id: true,
                        meeting_date: true,
                        meeting_type: true,
                        topic: true,
                        notes: true,
                        location: true,
                        offering: true,
                        is_realtime: true,
                        created_at: true,
                        _count: {
                            select: {
                                participants: true,
                                visitors: true,
                            },
                        },
                    },
                    orderBy: { meeting_date: "desc" },
                    skip,
                    take,
                }),
                database_1.prisma.attendanceMeeting.count({
                    where: {
                        ministry_id: id,
                        event_category: "ministry",
                    },
                }),
            ]);
            res.json({
                success: true,
                data: meetings,
                pagination: {
                    page: Number(page),
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.MinistryController = MinistryController;
//# sourceMappingURL=MinistryController.js.map