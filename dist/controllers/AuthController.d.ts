import { Request, Response, NextFunction } from "express";
import { AuthRequest } from "../middleware/auth";
export declare class AuthController {
    adminLogin(req: Request, res: Response, next: NextFunction): Promise<void>;
    memberLogin(req: Request, res: Response, next: NextFunction): Promise<Response<any, Record<string, any>> | undefined>;
    checkMember(req: Request, res: Response, next: NextFunction): Promise<void>;
    resetPassword(req: Request, res: Response, next: NextFunction): Promise<void>;
    refreshToken(req: Request, res: Response, next: NextFunction): Promise<void>;
    logout(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getCurrentUser(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=AuthController.d.ts.map