"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistrictController = void 0;
const express_validator_1 = require("express-validator");
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
class DistrictController {
    async getDistricts(req, res, next) {
        try {
            const { page = 1, limit = 10, search } = req.query;
            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);
            const where = { status: 'active' };
            if (search) {
                where.OR = [
                    { name: { contains: search, mode: 'insensitive' } },
                    { description: { contains: search, mode: 'insensitive' } },
                ];
            }
            const [districts, total] = await Promise.all([
                database_1.prisma.district.findMany({
                    where,
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        status: true,
                        created_at: true,
                        leader1: {
                            select: { id: true, first_name: true, last_name: true },
                        },
                        leader2: {
                            select: { id: true, first_name: true, last_name: true },
                        },
                        _count: {
                            select: {
                                cell_groups: { where: { status: 'active' } },
                                members: { where: { status: 'active' } },
                            },
                        },
                    },
                    orderBy: { name: 'asc' },
                    skip,
                    take,
                }),
                database_1.prisma.district.count({ where }),
            ]);
            res.json({
                success: true,
                data: districts,
                pagination: { page, limit: take, total, totalPages: Math.ceil(total / take) },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getDistrictById(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { id } = req.params;
            const district = await database_1.prisma.district.findUnique({
                where: { id },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    status: true,
                    created_at: true,
                    updated_at: true,
                    leader1: {
                        select: { id: true, first_name: true, last_name: true, email: true, phone: true },
                    },
                    leader2: {
                        select: { id: true, first_name: true, last_name: true, email: true, phone: true },
                    },
                    cell_groups: {
                        select: {
                            id: true,
                            name: true,
                            status: true,
                            _count: {
                                select: { cell_group_members: { where: { status: 'active' } } },
                            },
                        },
                        where: { status: 'active' },
                    },
                    _count: {
                        select: {
                            cell_groups: { where: { status: 'active' } },
                            members: { where: { status: 'active' } },
                        },
                    },
                },
            });
            if (!district) {
                throw (0, errorHandler_1.createError)('District not found', 404);
            }
            res.json({ success: true, data: district });
        }
        catch (error) {
            next(error);
        }
    }
    async createDistrict(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const districtData = req.body;
            if (districtData.leader1_id) {
                const leader1 = await database_1.prisma.member.findUnique({
                    where: { id: districtData.leader1_id },
                });
                if (!leader1) {
                    throw (0, errorHandler_1.createError)('Leader 1 not found', 400);
                }
            }
            if (districtData.leader2_id) {
                const leader2 = await database_1.prisma.member.findUnique({
                    where: { id: districtData.leader2_id },
                });
                if (!leader2) {
                    throw (0, errorHandler_1.createError)('Leader 2 not found', 400);
                }
            }
            const district = await database_1.prisma.district.create({
                data: districtData,
                select: {
                    id: true,
                    name: true,
                    description: true,
                    status: true,
                    created_at: true,
                },
            });
            res.status(201).json({ success: true, data: district });
        }
        catch (error) {
            next(error);
        }
    }
    async updateDistrict(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { id } = req.params;
            const updateData = req.body;
            const existingDistrict = await database_1.prisma.district.findUnique({
                where: { id },
            });
            if (!existingDistrict) {
                throw (0, errorHandler_1.createError)('District not found', 404);
            }
            const district = await database_1.prisma.district.update({
                where: { id },
                data: updateData,
                select: {
                    id: true,
                    name: true,
                    description: true,
                    status: true,
                    updated_at: true,
                },
            });
            res.json({ success: true, data: district });
        }
        catch (error) {
            next(error);
        }
    }
    async deleteDistrict(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { id } = req.params;
            const existingDistrict = await database_1.prisma.district.findUnique({
                where: { id },
            });
            if (!existingDistrict) {
                throw (0, errorHandler_1.createError)('District not found', 404);
            }
            await database_1.prisma.district.update({
                where: { id },
                data: { status: 'inactive' },
            });
            res.json({ success: true, data: { message: 'District deleted successfully' } });
        }
        catch (error) {
            next(error);
        }
    }
    async getDistrictCellGroups(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { id } = req.params;
            const cellGroups = await database_1.prisma.cellGroup.findMany({
                where: {
                    district_id: id,
                    status: 'active',
                },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    meeting_day: true,
                    meeting_time: true,
                    meeting_location: true,
                    status: true,
                    leader: {
                        select: { id: true, first_name: true, last_name: true },
                    },
                    assistant_leader: {
                        select: { id: true, first_name: true, last_name: true },
                    },
                    _count: {
                        select: { cell_group_members: { where: { status: 'active' } } },
                    },
                },
                orderBy: { name: 'asc' },
            });
            res.json({ success: true, data: cellGroups });
        }
        catch (error) {
            next(error);
        }
    }
    async getDistrictMembers(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { id } = req.params;
            const { page = 1, limit = 10 } = req.query;
            const skip = (Number(page) - 1) * Number(limit);
            const take = Math.min(Number(limit), 100);
            const [members, total] = await Promise.all([
                database_1.prisma.member.findMany({
                    where: {
                        district_id: id,
                        status: 'active',
                    },
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                        phone: true,
                        status: true,
                        join_date: true,
                        cell_group: {
                            select: { id: true, name: true },
                        },
                    },
                    orderBy: { last_name: 'asc' },
                    skip,
                    take,
                }),
                database_1.prisma.member.count({
                    where: {
                        district_id: id,
                        status: 'active',
                    },
                }),
            ]);
            res.json({
                success: true,
                data: members,
                pagination: {
                    page: Number(page),
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.DistrictController = DistrictController;
//# sourceMappingURL=DistrictController.js.map