import { Response, NextFunction } from "express";
import { AuthRequest } from "../middleware/auth";
export declare class ClassController {
    getClasses(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getClassById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createClass(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateClass(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteClass(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getClassLevels(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createClassLevel(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getClassSessions(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createClassSession(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getClassEnrollments(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    enrollMember(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    selfEnrollMember(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateEnrollment(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getLevelById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateLevel(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteLevel(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getSessionById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateSession(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteSession(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=ClassController.d.ts.map