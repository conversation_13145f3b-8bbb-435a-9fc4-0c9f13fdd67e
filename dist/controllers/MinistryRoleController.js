"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MinistryRoleController = void 0;
const express_validator_1 = require("express-validator");
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
class MinistryRoleController {
    static async autoCreateRoleIfNeeded(roleName) {
        if (!roleName || typeof roleName !== "string" || !roleName.trim()) {
            return;
        }
        const trimmedRole = roleName.trim();
        try {
            const roleExists = await database_1.prisma.ministryRole.findFirst({
                where: {
                    name: trimmedRole,
                    status: "active",
                },
            });
            if (!roleExists) {
                await database_1.prisma.ministryRole.create({
                    data: {
                        name: trimmedRole,
                        description: `Auto-created role: ${trimmedRole}`,
                        ministry_type: "general",
                        is_leadership: false,
                        sort_order: 999,
                    },
                });
                console.log(`Auto-created new ministry role: ${trimmedRole}`);
            }
        }
        catch (error) {
            console.log(`Failed to auto-create role ${trimmedRole}:`, error);
        }
    }
    async getMinistryRoles(req, res, next) {
        try {
            const { ministry_type, is_leadership } = req.query;
            const where = { status: "active" };
            if (ministry_type) {
                where.ministry_type = ministry_type;
            }
            if (is_leadership !== undefined) {
                where.is_leadership = is_leadership === "true";
            }
            const ministryRoles = await database_1.prisma.ministryRole.findMany({
                where,
                select: {
                    id: true,
                    name: true,
                    description: true,
                    ministry_type: true,
                    is_leadership: true,
                    sort_order: true,
                },
                orderBy: [
                    { ministry_type: "asc" },
                    { sort_order: "asc" },
                    { name: "asc" },
                ],
            });
            res.json({
                success: true,
                data: ministryRoles,
            });
        }
        catch (error) {
            console.error("Error fetching ministry roles:", error);
            next((0, errorHandler_1.createError)("Failed to fetch ministry roles", 500));
        }
    }
    async createMinistryRole(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                return next((0, errorHandler_1.createError)("Validation failed", 400));
            }
            const { name, description, ministry_type, is_leadership } = req.body;
            const existingRole = await database_1.prisma.ministryRole.findFirst({
                where: { name, status: "active" },
            });
            if (existingRole) {
                return next((0, errorHandler_1.createError)("Role with this name already exists", 400));
            }
            const newRole = await database_1.prisma.ministryRole.create({
                data: {
                    name,
                    description,
                    ministry_type,
                    is_leadership: is_leadership || false,
                },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    ministry_type: true,
                    is_leadership: true,
                },
            });
            res.status(201).json({
                success: true,
                data: newRole,
                message: "Ministry role created successfully",
            });
        }
        catch (error) {
            console.error("Error creating ministry role:", error);
            next((0, errorHandler_1.createError)("Failed to create ministry role", 500));
        }
    }
    async updateMinistryRole(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                return next((0, errorHandler_1.createError)("Validation failed", 400));
            }
            const { id } = req.params;
            const { name, description, ministry_type, is_leadership } = req.body;
            const existingRole = await database_1.prisma.ministryRole.findFirst({
                where: { id, status: "active" },
            });
            if (!existingRole) {
                return next((0, errorHandler_1.createError)("Ministry role not found", 404));
            }
            if (name && name !== existingRole.name) {
                const nameConflict = await database_1.prisma.ministryRole.findFirst({
                    where: { name, status: "active", id: { not: id } },
                });
                if (nameConflict) {
                    return next((0, errorHandler_1.createError)("Role with this name already exists", 400));
                }
            }
            const updatedRole = await database_1.prisma.ministryRole.update({
                where: { id },
                data: {
                    name,
                    description,
                    ministry_type,
                    is_leadership,
                    updated_at: new Date(),
                },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    ministry_type: true,
                    is_leadership: true,
                },
            });
            res.json({
                success: true,
                data: updatedRole,
                message: "Ministry role updated successfully",
            });
        }
        catch (error) {
            console.error("Error updating ministry role:", error);
            next((0, errorHandler_1.createError)("Failed to update ministry role", 500));
        }
    }
    async deleteMinistryRole(req, res, next) {
        try {
            const { id } = req.params;
            const existingRole = await database_1.prisma.ministryRole.findFirst({
                where: { id, status: "active" },
            });
            if (!existingRole) {
                return next((0, errorHandler_1.createError)("Ministry role not found", 404));
            }
            const roleInUse = await database_1.prisma.ministryMember.findFirst({
                where: { role: existingRole.name, status: "active" },
            });
            if (roleInUse) {
                return next((0, errorHandler_1.createError)("Cannot delete role that is currently being used by ministry members", 400));
            }
            await database_1.prisma.ministryRole.update({
                where: { id },
                data: {
                    status: "inactive",
                    updated_at: new Date(),
                },
            });
            res.json({
                success: true,
                message: "Ministry role deleted successfully",
            });
        }
        catch (error) {
            console.error("Error deleting ministry role:", error);
            next((0, errorHandler_1.createError)("Failed to delete ministry role", 500));
        }
    }
}
exports.MinistryRoleController = MinistryRoleController;
//# sourceMappingURL=MinistryRoleController.js.map