import { Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
export declare class DistrictController {
    getDistricts(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getDistrictById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createDistrict(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateDistrict(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteDistrict(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getDistrictCellGroups(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getDistrictMembers(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=DistrictController.d.ts.map