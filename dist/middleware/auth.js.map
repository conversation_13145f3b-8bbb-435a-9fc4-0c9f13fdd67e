{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,6CAA0C;AAC1C,iDAA4C;AAC5C,iDAA6C;AAWtC,MAAM,iBAAiB,GAAG,KAAK,EAClC,GAAgB,EAChB,GAAa,EACb,IAAkB,EACpB,EAAE;IACA,IAAI,CAAC;QACD,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;YAC1B,aAAa,EAAE,CAAC,CAAC,UAAU;YAC3B,WAAW,EAAE,KAAK,EAAE,MAAM;YAC1B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YACnC,MAAM,IAAA,0BAAW,EAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,eAAM,CAAC,UAAU,CAAQ,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE;YAC5B,MAAM,EAAE,OAAO,CAAC,EAAE;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;SACvB,CAAC,CAAC;QAGH,MAAM,IAAI,GAAG,MAAM,iBAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;YACzB,MAAM,EAAE;gBACJ,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,IAAI;aACf;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE;gBACzC,MAAM,EAAE,OAAO,CAAC,EAAE;aACrB,CAAC,CAAC;YACH,MAAM,IAAA,0BAAW,EAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE;YAC9B,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;SAClB,CAAC,CAAC;QACH,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,EAAE,CAAC;IACX,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CACT,eAAe,EACf,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CACjD,CAAC;QACF,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YACzC,IAAI,CAAC,IAAA,0BAAW,EAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;IACL,CAAC;AACL,CAAC,CAAC;AA/DW,QAAA,iBAAiB,qBA+D5B;AAEK,MAAM,YAAY,GAAG,CACxB,GAAgB,EAChB,GAAa,EACb,IAAkB,EACpB,EAAE;IACA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAdW,QAAA,YAAY,gBAcvB;AAEK,MAAM,aAAa,GAAG,CACzB,GAAgB,EAChB,GAAa,EACb,IAAkB,EACpB,EAAE;IACA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAVW,QAAA,aAAa,iBAUxB"}