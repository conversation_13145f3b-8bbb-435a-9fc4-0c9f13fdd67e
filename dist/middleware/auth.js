"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireMember = exports.requireAdmin = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config/config");
const database_1 = require("../config/database");
const errorHandler_1 = require("./errorHandler");
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(" ")[1];
        console.log("🔐 Auth check:", {
            hasAuthHeader: !!authHeader,
            tokenLength: token?.length,
            path: req.path,
            method: req.method,
        });
        if (!token) {
            console.log("❌ No token provided");
            throw (0, errorHandler_1.createError)("Access token required", 401);
        }
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.JWT_SECRET);
        console.log("✅ Token decoded:", {
            userId: decoded.id,
            email: decoded.email,
        });
        const user = await database_1.prisma.member.findUnique({
            where: { id: decoded.id },
            select: {
                id: true,
                email: true,
                role: true,
                role_level: true,
                status: true,
            },
        });
        if (!user || user.status !== "active") {
            console.log("❌ User not found or inactive:", {
                userId: decoded.id,
            });
            throw (0, errorHandler_1.createError)("User not found or inactive", 401);
        }
        console.log("✅ Auth successful:", {
            userId: user.id,
            role: user.role,
        });
        req.user = user;
        next();
    }
    catch (error) {
        console.error("❌ Auth error:", error instanceof Error ? error.message : error);
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            next((0, errorHandler_1.createError)("Invalid token", 401));
        }
        else {
            next(error);
        }
    }
};
exports.authenticateToken = authenticateToken;
const requireAdmin = (req, res, next) => {
    if (!req.user) {
        return next((0, errorHandler_1.createError)("Authentication required", 401));
    }
    if (req.user.role !== "admin" && req.user.role_level < 4) {
        return next((0, errorHandler_1.createError)("Admin access required", 403));
    }
    next();
};
exports.requireAdmin = requireAdmin;
const requireMember = (req, res, next) => {
    if (!req.user) {
        return next((0, errorHandler_1.createError)("Authentication required", 401));
    }
    next();
};
exports.requireMember = requireMember;
//# sourceMappingURL=auth.js.map