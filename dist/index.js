"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = exports.app = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
require("express-async-errors");
const config_1 = require("./config/config");
const errorHandler_1 = require("./middleware/errorHandler");
const notFoundHandler_1 = require("./middleware/notFoundHandler");
const auth_1 = __importDefault(require("./routes/auth"));
const members_1 = __importDefault(require("./routes/members"));
const cellGroups_1 = __importDefault(require("./routes/cellGroups"));
const districts_1 = __importDefault(require("./routes/districts"));
const attendance_1 = __importDefault(require("./routes/attendance"));
const classes_1 = __importDefault(require("./routes/classes"));
const ministries_1 = __importDefault(require("./routes/ministries"));
const ministryRoles_1 = __importDefault(require("./routes/ministryRoles"));
const articles_1 = __importDefault(require("./routes/articles"));
const articleCategories_1 = __importDefault(require("./routes/articleCategories"));
const enrollment_1 = __importDefault(require("./routes/enrollment"));
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: config_1.config.FRONTEND_URL,
        methods: ["GET", "POST"],
    },
});
exports.io = io;
const limiter = (0, express_rate_limit_1.default)({
    windowMs: config_1.config.RATE_LIMIT_WINDOW_MS,
    max: config_1.config.RATE_LIMIT_MAX_REQUESTS,
    message: "Too many requests from this IP, please try again later.",
});
app.use((0, helmet_1.default)());
app.use((0, compression_1.default)());
app.use(limiter);
app.use((0, cors_1.default)({
    origin: config_1.config.FRONTEND_URL,
    credentials: true,
}));
app.use((0, morgan_1.default)("combined"));
app.use(express_1.default.json({ limit: "10mb" }));
app.use(express_1.default.urlencoded({ extended: true }));
app.get("/health", (req, res) => {
    res.json({ status: "OK", timestamp: new Date().toISOString() });
});
app.use("/api/auth", auth_1.default);
app.use("/api/members", members_1.default);
app.use("/api/cell-groups", cellGroups_1.default);
app.use("/api/districts", districts_1.default);
app.use("/api/attendance", attendance_1.default);
app.use("/api/classes", classes_1.default);
app.use("/api/ministries", ministries_1.default);
app.use("/api/ministry-roles", ministryRoles_1.default);
app.use("/api/articles", articles_1.default);
app.use("/api/article-categories", articleCategories_1.default);
app.use("/api", enrollment_1.default);
app.use("/api/projects", require("./routes/projects").default);
app.use("/api/donations", require("./routes/donations").default);
io.on("connection", (socket) => {
    console.log("Client connected:", socket.id);
    socket.on("join-meeting", (meetingId) => {
        socket.join(`meeting-${meetingId}`);
        console.log(`Client ${socket.id} joined meeting ${meetingId}`);
    });
    socket.on("leave-meeting", (meetingId) => {
        socket.leave(`meeting-${meetingId}`);
        console.log(`Client ${socket.id} left meeting ${meetingId}`);
    });
    socket.on("disconnect", () => {
        console.log("Client disconnected:", socket.id);
    });
});
app.set("io", io);
app.use(notFoundHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
const PORT = config_1.config.PORT || 3001;
const HOST = "0.0.0.0";
server.listen(PORT, HOST, () => {
    console.log(`🚀 Server running on http://${HOST}:${PORT}`);
    console.log(`📊 Environment: ${config_1.config.NODE_ENV}`);
    console.log(`🔗 Frontend URL: ${config_1.config.FRONTEND_URL}`);
    console.log(`🌐 Accessible from network: http://*************:${PORT}`);
});
//# sourceMappingURL=index.js.map