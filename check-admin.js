const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkAdminUsers() {
  try {
    const users = await prisma.member.findMany({
      where: {
        OR: [
          { role: 'admin' },
          { role_level: { gte: 4 } }
        ]
      },
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        role: true,
        role_level: true,
        status: true,
        password_hash: true
      }
    });
    
    console.log('Admin users found:', users.length);
    users.forEach(user => {
      console.log('Email:', user.email);
      console.log('Role:', user.role);
      console.log('Role Level:', user.role_level);
      console.log('Status:', user.status);
      console.log('Has password hash:', user.password_hash ? 'Yes' : 'No');
      if (user.password_hash) {
        console.log('Password hash length:', user.password_hash.length);
      }
      console.log('---');
    });
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
  }
}

checkAdminUsers();
