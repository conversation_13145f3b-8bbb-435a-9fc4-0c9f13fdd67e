<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Church Management API Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ccc;
        }

        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }

        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }

        .endpoint {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🏛️ Church Management API Connection Tester</h1>
        <p>Test koneksi ke Node.js API di <strong>*************:3000</strong></p>

        <?php
        $nodeApiUrl = 'http://*************:3000';

        // Basic endpoints
        $basicEndpoints = [
            '/health' => 'Health Check'
        ];

        // Backend API routes to test
        $apiEndpoints = [
            '/api/auth' => 'Authentication API',
            '/api/members' => 'Members API',
            '/api/cell-groups' => 'Cell Groups API',
            '/api/districts' => 'Districts API',
            '/api/attendance' => 'Attendance API',
            '/api/classes' => 'Classes API',
            '/api/ministries' => 'Ministries API',
            '/api/articles' => 'Articles API',
            '/api/projects' => 'Projects API',
            '/api/donations' => 'Donations API'
        ];

        // Combine all endpoints
        $allEndpoints = array_merge($basicEndpoints, $apiEndpoints);

        if ($_POST['action'] ?? false) {
            echo '<h2>📊 Test Results:</h2>';

            $testType = $_POST['test_type'] ?? 'basic';
            $endpointsToTest = $testType === 'api' ? $apiEndpoints : ($testType === 'all' ? $allEndpoints : $basicEndpoints);

            foreach ($endpointsToTest as $endpoint => $description) {
                $url = $nodeApiUrl . $endpoint;
                echo "<div class='endpoint'>";
                echo "<h3>{$description}</h3>";
                echo "<strong>URL:</strong> <code>{$url}</code><br>";

                // Test connection
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
                curl_setopt($ch, CURLOPT_HEADER, true);
                curl_setopt($ch, CURLOPT_NOBODY, false);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

                $startTime = microtime(true);
                $response = curl_exec($ch);
                $endTime = microtime(true);
                $responseTime = round(($endTime - $startTime) * 1000, 2);

                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);

                if ($error) {
                    echo "<div class='test-result error'>";
                    echo "<strong>❌ Connection Failed</strong><br>";
                    echo "Error: {$error}<br>";
                    echo "Response time: {$responseTime}ms";
                    echo "</div>";
                } elseif ($httpCode >= 200 && $httpCode < 400) {
                    echo "<div class='test-result success'>";
                    echo "<strong>✅ Connection Successful</strong><br>";
                    echo "HTTP Status: {$httpCode}<br>";
                    echo "Response time: {$responseTime}ms<br>";

                    // Parse headers and body
                    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
                    $headers = substr($response, 0, $headerSize);
                    $body = substr($response, $headerSize);

                    if ($body && strlen($body) < 1000) {
                        echo "<strong>Response:</strong><br>";
                        echo "<pre>" . htmlspecialchars($body) . "</pre>";
                    }
                    echo "</div>";
                } elseif ($httpCode == 404 && strpos($endpoint, '/api/') !== false) {
                    // 404 for API routes might mean route exists but needs specific method/params
                    echo "<div class='test-result info'>";
                    echo "<strong>ℹ️ API Route Detected</strong><br>";
                    echo "HTTP Status: {$httpCode}<br>";
                    echo "Response time: {$responseTime}ms<br>";
                    echo "Note: API route exists but may require:<br>";
                    echo "• Specific HTTP method (POST, PUT, DELETE)<br>";
                    echo "• Authentication headers<br>";
                    echo "• Request parameters<br>";
                    echo "• Database connection";
                    echo "</div>";
                } else {
                    echo "<div class='test-result error'>";
                    echo "<strong>⚠️ Connection Issues</strong><br>";
                    echo "HTTP Status: {$httpCode}<br>";
                    echo "Response time: {$responseTime}ms";
                    echo "</div>";
                }
                echo "</div>";
            }

            // Additional network info
            echo '<h2>🌐 Network Information:</h2>';
            echo '<div class="test-result info">';
            echo '<strong>Server IP:</strong> ' . $_SERVER['SERVER_ADDR'] . '<br>';
            echo '<strong>Client IP:</strong> ' . $_SERVER['REMOTE_ADDR'] . '<br>';
            echo '<strong>User Agent:</strong> ' . $_SERVER['HTTP_USER_AGENT'] . '<br>';
            echo '<strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '<br>';
            echo '</div>';
        }
        ?>

        <form method="POST">
            <h3>🧪 Choose Test Type:</h3>
            <button type="submit" name="action" value="test" onclick="setTestType('basic')">🔍 Basic Test (Health Check)</button>
            <button type="submit" name="action" value="test" onclick="setTestType('api')">🚀 Backend API Routes Test</button>
            <button type="submit" name="action" value="test" onclick="setTestType('all')">🔥 Complete Test (All Routes)</button>
            <button type="button" onclick="location.reload()">🔄 Refresh Page</button>
            <input type="hidden" name="test_type" id="test_type" value="basic">
        </form>

        <script>
            function setTestType(type) {
                document.getElementById('test_type').value = type;
            }
        </script>

        <h2>📋 Available API Routes:</h2>
        <div class="endpoint">
            <h3>✅ Basic Endpoints:</h3>
            <?php foreach ($basicEndpoints as $endpoint => $description): ?>
                <strong><?php echo $description; ?>:</strong>
                <a href="<?php echo $nodeApiUrl . $endpoint; ?>" target="_blank">
                    <?php echo $nodeApiUrl . $endpoint; ?>
                </a><br>
            <?php endforeach; ?>
        </div>

        <div class="endpoint">
            <h3>🚀 Backend API Routes:</h3>
            <?php foreach ($apiEndpoints as $endpoint => $description): ?>
                <strong><?php echo $description; ?>:</strong>
                <a href="<?php echo $nodeApiUrl . $endpoint; ?>" target="_blank">
                    <?php echo $nodeApiUrl . $endpoint; ?>
                </a><br>
            <?php endforeach; ?>
        </div>

        <div class="test-result info">
            <strong>💡 Instructions:</strong><br>
            1. Upload file ini ke folder web server (public_html atau www)<br>
            2. Akses via browser: http://*************/api-tester.php<br>
            3. Klik "Run Connection Test" untuk test koneksi<br>
            4. Cek hasil test untuk memastikan Node.js API accessible
        </div>
    </div>
</body>

</html>